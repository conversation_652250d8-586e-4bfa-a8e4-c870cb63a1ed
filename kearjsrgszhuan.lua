--==《新武将》==--
extension = sgs.Package("kearjsrgszhuan", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

function KezhuanToData(self)
	local data = sgs.QVariant()
	if type(self)=="string"
	or type(self)=="boolean"
	or type(self)=="number"
	then data = sgs.QVariant(self)
	elseif self~=nil then data:setValue(self) end
	return data
end

--buff集中
kezhuanslashmore = sgs.CreateTargetModSkill{
	name = "kezhuanslashmore",
	pattern = "^SkillCard",
	residue_func = function(self, from, card, to)
		local n = 0
		if (card:getSkillName() == "kezhuanzhenfeng")and from:hasSkill("kezhuanzhenfeng") then
			n = n + 1000
		end
		return n
	end,
	extra_target_func = function(self, from, card)
		local n = 0
		--[[if (from:getMark("&kechengneifaNotBasic") > 0 and card:isNDTrick()) then
			n = n + 1
		end]]
		return n
	end,
	distance_limit_func = function(self, from, card, to)
		local n = 0
		if (card:getSkillName() == "kezhuancuifeng") then
			n = n + 1000
		end
		--[[if from:hasSkill("kezhuanfuni") and card:isKindOf("Slash") then
			n = -from:getAttackRange()
			n = -100
		end]]
		if card:isKindOf("Slash") and to and to:getMark("kezhuanrihui-Clear")<1
		and to:getJudgingArea():length()<1 and from:hasSkill("kezhuanrihui") then
			n = n + 1000
		end
		if (card:getSkillName() == "kezhuanzhenfeng") and from:hasSkill("kezhuanzhenfeng") then
			n = n + 1000
		end
		if (from:getMark("&kezhuanfuni-Clear")>0) then
			n = n + 1000
		end
		return n
	end
}
if not sgs.Sanguosha:getSkill("kezhuanslashmore") then skills:append(kezhuanslashmore) end

kezhuanguojia = sgs.General(extension, "kezhuanguojia", "wei", 3,true)

kezhuanqingzi = sgs.CreateTriggerSkill{
    name = "kezhuanqingzi",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart,sgs.Death},
	waked_skills = "tenyearshensu",
	can_trigger = function(self, target)
		return target and target:hasSkill(self:objectName())
	end ,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.Death) then
			local death = data:toDeath()
			if death.who:objectName() ~= player:objectName() then return false end
			for _, p in sgs.qlist(room:getAllPlayers()) do 
				if (p:getMark("&kezhuanqingzi") > 0) then
					room:setPlayerMark(p,"&kezhuanqingzi",0)
					room:handleAcquireDetachSkills(p, "-tenyearshensu")
				end
			end		
		end
		if (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Start) then
				local players = sgs.SPlayerList()
				for _,p in sgs.qlist(room:getOtherPlayers(player)) do
					if player:canDiscard(p, "e") then
						players:append(p)
					end
				end	
				local ones = room:askForPlayersChosen(player, players, self:objectName(), 0, players:length(), "kezhuanqingzi-ask", true, true)
				if not ones:isEmpty() then room:broadcastSkillInvoke(self:objectName()) end
				for _,q in sgs.qlist(ones) do
					local to_throw = room:askForCardChosen(player, q, "e", self:objectName(),false,sgs.Card_MethodDiscard)
					room:throwCard(to_throw, q, player)
					if not q:hasSkill("tenyearshensu",true) then
						room:setPlayerMark(q,"&kezhuanqingzi",1)
						room:handleAcquireDetachSkills(q, "tenyearshensu")
					end
				end	
			end
			if (player:getPhase() == sgs.Player_RoundStart) then
				for _,p in sgs.qlist(room:getAllPlayers()) do
					if (p:getMark("&kezhuanqingzi") > 0) then
				        room:handleAcquireDetachSkills(p, "-tenyearshensu")
						room:setPlayerMark(p,"&kezhuanqingzi",0)
					end
				end
			end
		end
	end,
}
kezhuanguojia:addSkill(kezhuanqingzi)

kezhuandingce = sgs.CreateTriggerSkill{
	name = "kezhuandingce" ,
	events = {sgs.Damaged} ,
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if event == sgs.Damaged then
			local to_data = sgs.QVariant()
			to_data:setValue(damage.from)
			if player:askForSkillInvoke(self, to_data) then
				room:broadcastSkillInvoke(self:objectName())
				local cs = {}
				if player:canDiscard(player, "h") then
					local id = room:askForCardChosen(player,player,"h",self:objectName(),false,sgs.Card_MethodDiscard)
					room:throwCard(id, player, player)
					table.insert(cs,sgs.Sanguosha:getCard(id):getColor())
				end
				if player:isAlive() and player:canDiscard(damage.from, "h") then
					local id = room:askForCardChosen(player,damage.from,"h",self:objectName(),false,sgs.Card_MethodDiscard)
					room:throwCard(id, damage.from, player)
					table.insert(cs,sgs.Sanguosha:getCard(id):getColor())
				end
				if #cs>1 and cs[1]==cs[2] and player:isAlive() then
					local dzxj = sgs.Sanguosha:cloneCard("dongzhuxianji")
					dzxj:setSkillName("_kezhuandingce")
					dzxj:deleteLater()  
					if player:canUse(dzxj,player) then
						room:useCard(sgs.CardUseStruct(dzxj,player,player), true)
					end
				end
			end
		end
	end
}
kezhuanguojia:addSkill(kezhuandingce)


function zhuanJfNames(player)
	local aps = player:getAliveSiblings()
	aps:append(player)
	local ption = ""
	for _,p in sgs.list(aps)do
		for _,s in sgs.list(p:getSkillList())do
			if s:isAttachedLordSkill() then continue end
			ption = ption..s:getDescription()
		end
	end
	local names = {}
	for id=0,sgs.Sanguosha:getCardCount()-1 do
		local c = sgs.Sanguosha:getEngineCard(id)
		if c:getTypeId()>2 or table.contains(names,c:objectName())
		or player:getMark(c:getType().."kezhuanzhenfeng-PlayClear")>0 then continue end
		if string.find(ption,"【"..sgs.Sanguosha:translate(c:objectName()).."】")
		and (c:isNDTrick() or c:isKindOf("BasicCard")) and (not c:isKindOf("kezhuan_ying"))
		then table.insert(names,c:objectName()) end
	end
	return names
end

function zhuandummyCard(name,suit,number)
	name = name or "slash"
	local c = sgs.Sanguosha:cloneCard(name)
	if c then
		if suit then c:setSuit(suit) end
		if number then c:setNumber(number) end
		c:deleteLater()
		return c
	end
end

kezhuanzhenfengCard = sgs.CreateSkillCard{
	name = "kezhuanzhenfengCard",
	target_fixed = true,
	about_to_use = function(self,room,use)
		local p_choices = {}
		for _,p in sgs.list(zhuanJfNames(use.from))do
			local dc = zhuandummyCard(p)
			dc:setSkillName("kezhuanzhenfeng")
			if use.from:getMark(dc:getType().."kezhuanzhenfeng-PlayClear")<1 and dc:isAvailable(use.from)
			then table.insert(p_choices,p) end
		end
		if #p_choices<1 then return end
		table.insert(p_choices,"cancel")
		p_choices = room:askForChoice(use.from,"kezhuanzhenfeng",table.concat(p_choices,"+"))
		if p_choices=="cancel" then return end
		for i=0,sgs.Sanguosha:getCardCount()-1 do
			local c = sgs.Sanguosha:getEngineCard(i)
			if c:objectName()==p_choices then
				room:setPlayerMark(use.from,"kezhuanzhenfeng_id",i)
				room:askForUseCard(use.from,"@@kezhuanzhenfeng","kezhuanzhenfeng1:"..p_choices,-1,sgs.Card_MethodPlay)
				break
			end
		end
	end
}
kezhuanzhenfengvs = sgs.CreateViewAsSkill{
	name = "kezhuanzhenfeng",
	view_as = function(self,cards)
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		if pattern=="@@kezhuanzhenfeng" then
			pattern = sgs.Self:getMark("kezhuanzhenfeng_id")
			pattern = sgs.Sanguosha:getEngineCard(pattern)
			pattern = sgs.Sanguosha:cloneCard(pattern:objectName())
			pattern:setSkillName("kezhuanzhenfeng")
			return pattern
		elseif sgs.Sanguosha:getCurrentCardUseReason()~=sgs.CardUseStruct_CARD_USE_REASON_PLAY
		and pattern~="" then
			local gn = zhuanJfNames(sgs.Self)
			for _,p in sgs.list(pattern:split("+"))do
				local dc = sgs.Sanguosha:cloneCard(p)
				dc:setSkillName("kezhuanzhenfeng")
				if table.contains(gn,p)
				then return dc end
				dc:deleteLater()
			end
			return false
		end
		return kezhuanzhenfengCard:clone()
	end,
	enabled_at_response = function(self,player,pattern)
	   	if pattern=="@@kezhuanzhenfeng" then return true
		elseif sgs.Sanguosha:getCurrentCardUseReason()~=sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
		or player:getPhase()~=sgs.Player_Play then return end
		local gn = zhuanJfNames(player)
		for _,p in sgs.list(pattern:split("+"))do
			if table.contains(gn,p)
			then return true end
		end
	end,
	enabled_at_nullification = function(self,player)				
	   	return player:getPhase()==sgs.Player_Play
		and player:getMark("trickkezhuanzhenfeng-PlayClear")<1
	end,
	enabled_at_play = function(self,player)
		for _,p in sgs.list(zhuanJfNames(player))do
			local dc = zhuandummyCard(p)
			dc:setSkillName("kezhuanzhenfeng")
			if player:getMark(dc:getType().."kezhuanzhenfeng-PlayClear")<1 and dc:isAvailable(player)
			then return true end
		end
	end,
}

kezhuanzhenfeng = sgs.CreateTriggerSkill{
	name = "kezhuanzhenfeng",
	events = {sgs.PostCardEffected,sgs.PreCardUsed,sgs.CardOnEffect},
	view_as_skill = kezhuanzhenfengvs,
	can_trigger = function(self,target)
		return target and target:isAlive()
	end,
	on_trigger = function(self,event,player,data,room)
		if event==sgs.PostCardEffected then
            local effect = data:toCardEffect()
			if effect.card:getSkillName()=="kezhuanzhenfeng"
			and effect.card:hasFlag("kezhuanzhenfengBf") then
				for _,s in sgs.list(effect.to:getVisibleSkillList())do
					if s:isAttachedLordSkill() then continue end
					if string.find(s:getDescription(),"【"..sgs.Sanguosha:translate(effect.card:objectName()).."】") then
						room:getThread():delay(500)
						room:sendCompulsoryTriggerLog(effect.from,self:objectName())
						room:damage(sgs.DamageStruct(self:objectName(),effect.from,effect.to))
						break
					end
				end
			end
		elseif event==sgs.CardOnEffect then
            local effect = data:toCardEffect()
			if effect.card:getSkillName()=="kezhuanzhenfeng" and effect.to==player
			then room:setCardFlag(effect.card,"kezhuanzhenfengBf") end
		else
			local use = data:toCardUse()
			if use.card:getSkillName()=="kezhuanzhenfeng" then
				room:addPlayerMark(player,use.card:getType().."kezhuanzhenfeng-PlayClear")
			end
		end
		return false
	end
}
kezhuanguojia:addSkill(kezhuanzhenfeng)

kezhuan_ying = sgs.CreateBasicCard{
	name = "_kezhuan_ying",
	class_name = "KezhuanYing",
	subtype = "kespecial_card",
    can_recast = false,
	damage_card = false,
    available = function(self,player)
		return false
    end,
}
for i=0,16 do
	local card = kezhuan_ying:clone()
	card:setSuit(0)
	card:setNumber(1)
	card:setParent(extension)
end
kezhuanYing = sgs.CreateTriggerSkill{
	name = "#kezhuanYing",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardsMoveOneTime},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if (move.to_place == sgs.Player_DiscardPile) then
				for _,id in sgs.qlist(move.card_ids) do
					if sgs.Sanguosha:getCard(id):isKindOf("KezhuanYing")
					and room:getCardPlace(id)==sgs.Player_DiscardPile then
						local log = sgs.LogMessage()
						log.type = "$kezhuandestroyEquip"
						log.card_str = tostring(id)
						room:sendLog(log)
						kezhuandestroyEquip(room,id)
					end
				end
			end
		end		
	end,
}
if not sgs.Sanguosha:getSkill("kezhuanYing") then skills:append(kezhuanYing) end


kezhuanzhangren = sgs.General(extension, "kezhuanzhangren", "qun", 4)

function kezhuandestroyEquip(room, id)
	local move1 = sgs.CardsMoveStruct(id,nil,sgs.Player_PlaceTable,sgs.CardMoveReason(sgs.CardMoveReason_S_MASK_BASIC_REASON,"","destroy_equip",""))
	room:moveCardsAtomic(move1, true)
end

kezhuanfuni = sgs.CreateTriggerSkill{
	name = "kezhuanfuni",
	waked_skills = "#kezhuanfuniex",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardsMoveOneTime,sgs.RoundStart,sgs.TargetSpecified},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetSpecified) then
			local use = data:toCardUse()
			if (use.from:getMark("&kezhuanfuni-Clear") > 0)
			and (use.card:isKindOf("BasicCard") or use.card:isKindOf("TrickCard")) then
				local log = sgs.LogMessage()
				log.type = "$kezhuanfunixiangying"
				log.from = player
				room:sendLog(log)
				local no_respond_list = use.no_respond_list
				table.insert(no_respond_list, "_ALL_TARGETS")
				use.no_respond_list = no_respond_list
				data:setValue(use)
			end
		end
		if (event == sgs.RoundStart) then
			local ids = sgs.IntList()
			local num = math.ceil(player:aliveCount()/2)
			for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards(true)) do
				if sgs.Sanguosha:getEngineCard(id):isKindOf("KezhuanYing")
				and room:getCardOwner(id) == nil then
				    ids:append(id)
					if ids:length()>=num then break end
				end
			end
			if ids:length()>0 then
				room:sendCompulsoryTriggerLog(player,self)
				player:assignmentCards(ids,self:objectName(),room:getAlivePlayers(),-1,ids:length(),true)--[[
				local dummy = sgs.Sanguosha:cloneCard("slash")
				dummy:addSubcards(ids)
				player:obtainCard(dummy)
				dummy:deleteLater()
				local origin_yiji = sgs.IntList()
				for _, id in sgs.qlist(ids) do
					origin_yiji:append(id)
				end
				while room:askForYiji(player, ids, self:objectName(), true, false, true, -1, room:getAlivePlayers(),sgs.CardMoveReason(), "kezhuanfuni-distribute") do
					for _, id in sgs.qlist(origin_yiji) do
						if room:getCardOwner(id) ~= player then
							ids:removeOne(id)
						end
					end
					origin_yiji = sgs.IntList()
					for _, id in sgs.qlist(ids) do
						origin_yiji:append(id)
					end
					if not player:isAlive() then return end
				end]]
			end
		end
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if (move.to_place == sgs.Player_DiscardPile) then
				for _,id in sgs.qlist(move.card_ids) do
					if sgs.Sanguosha:getCard(id):isKindOf("KezhuanYing") then
						room:setPlayerMark(player,"&kezhuanfuni-Clear",1)
						break
					end
				end
			end
		end		
	end,
}
kezhuanzhangren:addSkill(kezhuanfuni)

kezhuanfuniex = sgs.CreateAttackRangeSkill{
	name = "#kezhuanfuniex",
	fixed_func = function(self, target)
		if target:hasSkill("kezhuanfuni") then
			return 0			
		end
		return -1
	end--[[
	extra_func = function(self, target)
		local n = 0
		if target:hasSkill("kezhuanfuni") then
			n = n -999
		end
		return n
	end--]]
}
kezhuanzhangren:addSkill(kezhuanfuniex)

kezhuanchuanxinCard = sgs.CreateSkillCard{
	name = "kezhuanchuanxinCard" ,
	mute = true,
	filter = function(self, targets, to_select, source)
		local targets_list = sgs.PlayerList()
		for _, target in ipairs(targets) do
			targets_list:append(target)
		end
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("kezhuanchuanxin")
		slash:deleteLater()
		return slash:targetFilter(targets_list, to_select, source)
	end ,
	on_use = function(self, room, source, targets)
		local targets_list = sgs.SPlayerList()
		for _, target in ipairs(targets) do
			if source:canSlash(target, nil, false) then
				targets_list:append(target)
			end
		end
		if targets_list:length() > 0 then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("kezhuanchuanxin")
			slash:addSubcard(self)
			room:useCard(sgs.CardUseStruct(slash, source, targets_list))
		end
	end
}
kezhuanchuanxinVS = sgs.CreateViewAsSkill{
	name = "kezhuanchuanxin" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("kezhuanchuanxin")
		slash:addSubcard(to_select)
		return not sgs.Self:isLocked(slash)
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return end
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("kezhuanchuanxin")
		for _, cd in ipairs(cards) do
			slash:addSubcard(cd)
		end
		return slash
	end ,
	enabled_at_play = function()
		return false
	end ,
	enabled_at_response = function(self, player, pattern)
		return pattern:startsWith("@@kezhuanchuanxin")
	end
}
kezhuanchuanxin = sgs.CreateTriggerSkill{
	name = "kezhuanchuanxin" ,
	events = {sgs.EventPhaseStart,sgs.DamageCaused,sgs.HpRecover} ,
	view_as_skill = kezhuanchuanxinVS,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Finish) then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasSkill(self) and not p:isNude() then
						for _, pp in sgs.qlist(room:getAllPlayers()) do
							if p:canSlash(pp) then
								room:askForUseCard(p, "@@kezhuanchuanxin", "kezhuanchuanxin-ask")
								break
							end
						end
					end
				end
			end
		end
		if (event == sgs.DamageCaused) then
			local damage = data:toDamage()
			local n = damage.to:getMark("kezhuanchuanxin-Clear")
			if damage.from:hasSkill(self) and n>0 and damage.card
			and damage.card:getSkillName() == "kezhuanchuanxin" then
				room:sendCompulsoryTriggerLog(damage.from,self:objectName())
				local log = sgs.LogMessage()
				log.type = "$kezhuanchuanxinda"
				log.from = player
				log.arg = n
				room:sendLog(log)
				damage.damage = damage.damage + n
				data:setValue(damage)
			end
		end
		if (event == sgs.HpRecover) then
			local recover = data:toRecover()
			room:addPlayerMark(player,"kezhuanchuanxin-Clear",recover.recover)
		end
	end
}
kezhuanzhangren:addSkill(kezhuanchuanxin)




kezhuanmachao = sgs.General(extension, "kezhuanmachao", "qun", 4)

kezhuanzhuiming = sgs.CreateTriggerSkill{
	name = "kezhuanzhuiming",
	events = {sgs.TargetSpecified,sgs.ConfirmDamage},
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.ConfirmDamage) then
			local damage = data:toDamage()
			if damage.card and damage.card:hasFlag("kezhuanzhuimingcard") then
				room:sendCompulsoryTriggerLog(damage.from,self)
				damage.damage = damage.damage + 1
				data:setValue(damage)
			end
		end
		if (event == sgs.TargetSpecified) then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") and (use.to:length() == 1) then
				local target = use.to:at(0)
				local to_data = sgs.QVariant()
				to_data:setValue(target)
				if player:askForSkillInvoke(self, to_data) then
					room:broadcastSkillInvoke(self:objectName())
					local result = room:askForChoice(player, self:objectName(),"red+black",to_data)
					local log = sgs.LogMessage()
					log.type = "$kezhuanzhuiming"
					log.from = player
					log.arg = result
					room:sendLog(log)
					to_data:setValue(player)
					target:setTag("kezhuanzhuimingFrom",to_data)
					room:getThread():delay(300)
					room:askForDiscard(target,self:objectName(),999,0,true,true,"zhuiming_dis:"..result)
					room:getThread():delay(200)
					if target:getCardCount()>0 then
						local to_show = room:askForCardChosen(player, target, "he", self:objectName())
						room:showCard(target,to_show)
						if sgs.Sanguosha:getCard(to_show):getColorString()==result then
							local log = sgs.LogMessage()
							log.type = "$kezhuanzhuimingtrigger"
							log.from = player
							room:sendLog(log)
							use.m_addHistory = false
							local no_respond_list = use.no_respond_list
							for _, szm in sgs.qlist(use.to) do
								table.insert(no_respond_list, szm:objectName())
							end
							use.no_respond_list = no_respond_list
							data:setValue(use)
							room:setCardFlag(use.card,"kezhuanzhuimingcard")
						end
					end
				end
			end
		end
	end
}
kezhuanmachao:addSkill(kezhuanzhuiming)
kezhuanmachao:addSkill("mashu")




kezhuanzhangfei = sgs.General(extension, "kezhuanzhangfei", "shu", 5)

kezhuanbaohe = sgs.CreateTriggerSkill{
	name = "kezhuanbaohe" ,
	events = {sgs.EventPhaseEnd,sgs.CardResponded,sgs.DamageCaused,sgs.CardFinished} ,
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardFinished) then
			local use = data:toCardUse()
			if (use.card:getSkillName() == "kezhuanbaohe") then
				use.card:removeTag("kezhuanbaoheda")
			end
			if use.whocard and use.whocard:getSkillName()=="kezhuanbaohe" then
				local n = use.whocard:getTag("kezhuanbaoheda"):toInt()+1
				use.whocard:setTag("kezhuanbaoheda",sgs.QVariant(n))
			end
		end
		if (event == sgs.EventPhaseEnd) then
			if (player:getPhase() == sgs.Player_Play) then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasSkill(self:objectName()) and (p:getCardCount() >= 2) then
						local players = sgs.SPlayerList()
						local slash = sgs.Sanguosha:cloneCard("slash")
						slash:setSkillName("kezhuanbaohe")
						slash:deleteLater()  
						for _, pp in sgs.qlist(room:getOtherPlayers(p)) do
							if pp:inMyAttackRange(player) and p:canSlash(pp,slash,false)
							then players:append(pp) end	
						end
						p:setTag("kezhuanbaoheWho",KezhuanToData(player))
						if room:askForDiscard(p,self:objectName(),2,2,true,true,"kezhuanbaohe-ask:"..player:objectName(),".",self:objectName()) then
							room:useCard(sgs.CardUseStruct(slash,p,players), true)
						end
					end
				end
			end
		end
		if (event == sgs.CardResponded) then
			local response = data:toCardResponse()
			local restocard = response.m_toCard
			if (restocard:getSkillName() == "kezhuanbaohe") then
				local num = restocard:getTag("kezhuanbaoheda"):toInt() + 1
				restocard:setTag("kezhuanbaoheda", sgs.QVariant(num))
			end
		end
		if (event == sgs.DamageCaused) then
			local damage = data:toDamage()
			if damage.card and (damage.card:getSkillName() == "kezhuanbaohe") then
				local n = damage.card:getTag("kezhuanbaoheda"):toInt()
				if n<1 then return false end
				room:sendCompulsoryTriggerLog(damage.from,self:objectName())
				local log = sgs.LogMessage()
				log.type = "$kezhuanbaoheda"
				log.from = player
				log.arg = n
				room:sendLog(log)
				damage.damage = damage.damage + n
				data:setValue(damage)
			end
		end
	end,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
}
kezhuanzhangfei:addSkill(kezhuanbaohe)

kezhuanxushiCard = sgs.CreateSkillCard{
	name = "kezhuanxushiCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		return #targets < self:subcardsLength()
		and (to_select:objectName() ~= player:objectName()) 
	end,
	feasible = function(self,targets)
		return #targets==self:subcardsLength()
	end,
	about_to_use = function(self,room,use)
		local data = sgs.QVariant()
		data:setValue(use)
		use.from:setTag("kezhuanxushiUse",data)
		self:cardOnUse(room,use)
	end,
	on_use = function(self, room, player, targets)
		local use = player:getTag("kezhuanxushiUse"):toCardUse()
		local ids = self:getSubcards()
		local n = 0
		for i, p in sgs.qlist(use.to) do
			if p:isDead() then continue end
			room:giveCard(player,p,sgs.Sanguosha:getCard(ids:at(i)),self:getSkillName())
			n = n+1
		end
		if player:isDead() then return end
		local dummy = sgs.Sanguosha:cloneCard("slash")
		for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards(true)) do
			if sgs.Sanguosha:getEngineCard(id):isKindOf("KezhuanYing")
			and room:getCardOwner(id)==nil then
				dummy:addSubcard(id)
				if dummy:subcardsLength()>=n*2 then break end
			end
		end
		dummy:deleteLater()
		if dummy:subcardsLength()>0 then
			player:obtainCard(dummy)
		end
	end
}

kezhuanxushi = sgs.CreateViewAsSkill{
	name = "kezhuanxushi",
	n = 998,
	view_filter = function(self, selected, to_select)
		return #selected<sgs.Self:getAliveSiblings():length()
	end ,
	view_as = function(self, cards)
		local dc = kezhuanxushiCard:clone()
		for _, c in ipairs(cards) do
			dc:addSubcard(c)
		end
		return dc
	end ,
	enabled_at_play = function(self, player)
		return (not player:hasUsed("#kezhuanxushiCard")) and (not player:isNude())
	end, 
}
kezhuanzhangfei:addSkill(kezhuanxushi)



kezhuanxiahourong = sgs.General(extension, "kezhuanxiahourong", "wei", 4)

kezhuanfenjianCard = sgs.CreateSkillCard{
	name = "kezhuanfenjianCard",
	target_fixed = false,
	will_throw = false,
	mute = true,
	filter = function(self, targets, to_select,player)
		local duel = sgs.Sanguosha:cloneCard("duel")
		duel:setSkillName("kezhuanfenjian")
		return #targets < 1 and to_select:objectName() ~= player:objectName()
		and not player:isProhibited(to_select, duel)
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:setPlayerMark(player,"&kezhuanfenjianduel-Clear",1)
		local duel = sgs.Sanguosha:cloneCard("duel")
		duel:setSkillName("kezhuanfenjian")
		local card_use = sgs.CardUseStruct()
		card_use.from = player
		card_use.to:append(target)
		card_use.card = duel
		room:useCard(card_use, false)    
		duel:deleteLater() 
	end
}

kezhuanfenjianvs = sgs.CreateViewAsSkill{
	name = "kezhuanfenjian",
	n = 0,
	view_as = function(self, cards)
		local duel = sgs.Sanguosha:cloneCard("duel")
		duel:setSkillName("kezhuanfenjian")
		return duel
	end,
	enabled_at_play = function(self, player)
		return player:getMark("&kezhuanfenjian+:+duel-Clear") < 1--(not player:hasUsed("#kezhuanfenjianCard"))
	end,
}
kezhuanfenjian = sgs.CreateTriggerSkill{
	name = "kezhuanfenjian",
	view_as_skill = kezhuanfenjianvs,
	events = {sgs.AskForPeaches,sgs.DamageInflicted,sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.AskForPeaches) then
			local dying = data:toDying()
			if dying.who~=player and player:getMark("&kezhuanfenjian+:+peach-Clear") < 1 then
				local to_data = sgs.QVariant()
				to_data:setValue(dying.who)
				local peach = sgs.Sanguosha:cloneCard("peach")
				peach:setSkillName("kezhuanfenjian")
				peach:deleteLater()
				if player:canUse(peach,dying.who) and player:askForSkillInvoke(self, to_data,false) then
					room:useCard(sgs.CardUseStruct(peach,player,dying.who), true)
				end
			end
		end
		if (event == sgs.DamageInflicted) then
			local damage = data:toDamage()
			local n = damage.to:getMark("&kezhuanfenjian+:+peach-Clear")+damage.to:getMark("&kezhuanfenjian+:+duel-Clear")
			if n>0 then
				room:sendCompulsoryTriggerLog(damage.to,self)
				damage.damage = damage.damage + n
				data:setValue(damage)
			end
		end
		if (event == sgs.PreCardUsed) then
			local use = data:toCardUse()
			if use.card:getTypeId()>0 and use.card:getSkillName()=="kezhuanfenjian" then
				room:addPlayerMark(player,"&kezhuanfenjian+:+"..use.card:objectName().."-Clear")
			end
		end
	end,
}
kezhuanxiahourong:addSkill(kezhuanfenjian)



kezhuansunshuangxiang = sgs.General(extension, "kezhuansunshuangxiang", "wu", 3,false)

kezhuanguijiCard = sgs.CreateSkillCard{
	name = "kezhuanguijiCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		return #targets < 1 and (to_select:getGender() == sgs.General_Male)
		and (to_select:getHandcardNum() < player:getHandcardNum())
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:setPlayerMark(target,"&kezhuanguiji+#"..player:objectName(),1)
		room:addPlayerMark(player,"usekezhuanguiji")
		player:setFlags("kezhuanguijiTarget")
		target:setFlags("kezhuanguijiTarget")
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p:objectName() ~= player:objectName() and p:objectName() ~= target:objectName() then
				room:doNotify(p, sgs.CommandType.S_COMMAND_EXCHANGE_KNOWN_CARDS, json.encode({player:objectName(), target:objectName()}))
			end
		end
		local exchangeMove = sgs.CardsMoveList()
		local move1 = sgs.CardsMoveStruct(player:handCards(), target, sgs.Player_PlaceHand, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, player:objectName(), target:objectName(), "kezhuanguiji", ""))
		local move2 = sgs.CardsMoveStruct(target:handCards(), player, sgs.Player_PlaceHand, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, target:objectName(), player:objectName(), "kezhuanguiji", ""))
		exchangeMove:append(move1)
		exchangeMove:append(move2)
		room:moveCardsAtomic(exchangeMove, false)
		player:setFlags("-kezhuanguijiTarget")
		target:setFlags("-kezhuanguijiTarget")
	end
}

kezhuanguijiVS = sgs.CreateViewAsSkill{
	name = "kezhuanguiji",
	n = 0,
	view_as = function(self, cards)
		return kezhuanguijiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("usekezhuanguiji")<1 and (not player:hasUsed("#kezhuanguijiCard")) 
	end, 
}

kezhuanguiji = sgs.CreateTriggerSkill{
	name = "kezhuanguiji",
	events = {sgs.EventPhaseEnd,sgs.Death},
	view_as_skill = kezhuanguijiVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseEnd) then
			if (player:getPhase() == sgs.Player_Play) then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if player:getMark("&kezhuanguiji+#"..p:objectName()) > 0 then
						room:setPlayerMark(player,"&kezhuanguiji+#"..p:objectName(),0)
						room:setPlayerMark(p,"usekezhuanguiji",0)
						if p:askForSkillInvoke(self, KezhuanToData(player)) then
							room:broadcastSkillInvoke(self:objectName())
							player:setFlags("kezhuanguijiTarget")
							p:setFlags("kezhuanguijiTarget")
							for _, p in sgs.qlist(room:getAlivePlayers()) do
								if p:objectName() ~= player:objectName() and p:objectName() ~= p:objectName() then
									room:doNotify(p, sgs.CommandType.S_COMMAND_EXCHANGE_KNOWN_CARDS, json.encode({player:objectName(), p:objectName()}))
								end
							end
							local exchangeMove = sgs.CardsMoveList()
							local move1 = sgs.CardsMoveStruct(player:handCards(), p, sgs.Player_PlaceHand, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, player:objectName(), p:objectName(), "kezhuanguiji", ""))
							local move2 = sgs.CardsMoveStruct(p:handCards(), player, sgs.Player_PlaceHand, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, p:objectName(), player:objectName(), "kezhuanguiji", ""))
							exchangeMove:append(move1)
							exchangeMove:append(move2)
							room:moveCardsAtomic(exchangeMove, false)
							player:setFlags("-kezhuanguijiTarget")
							p:setFlags("-kezhuanguijiTarget")
						end
					end
				end
			end
		end
		if (event == sgs.Death) then
			local death = data:toDeath()
			if death.who == player then
				for _, p in sgs.qlist(room:getAllPlayers()) do 
					if (player:getMark("&kezhuanguiji+#"..p:objectName()) > 0) then
						room:setPlayerMark(p,"usekezhuanguiji",0)
					end
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end
}
kezhuansunshuangxiang:addSkill(kezhuanguiji)


kezhuanjiaohaoCard = sgs.CreateSkillCard{
	name = "kezhuanjiaohaoCard",
	will_throw = false,
	mute = true,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select, liubei)
		if #targets ~= 0 or (to_select:objectName() == liubei:objectName())
		or (not to_select:hasSkill("kezhuanjiaohao")) then return false end
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local equip = card:getRealCard():toEquipCard()
		local equip_index = equip:location()
		return to_select:getEquip(equip_index) == nil
	end,
	on_effect = function(self, effect)
		local liubei = effect.from
		liubei:getRoom():broadcastSkillInvoke("kezhuanjiaohao",math.random(3,4))
		liubei:getRoom():moveCardTo(self, liubei, effect.to, sgs.Player_PlaceEquip,sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, liubei:objectName(), "kezhuanjiaohao", ""))
	end
}
kezhuanjiaohaoex = sgs.CreateOneCardViewAsSkill{
	name = "kezhuanjiaohaoex&",	
	filter_pattern = "EquipCard|.|.|hand",
	view_as = function(self, card)
		local kezhuanjiaohao_card = kezhuanjiaohaoCard:clone()
		kezhuanjiaohao_card:addSubcard(card)
		kezhuanjiaohao_card:setSkillName(self:objectName())
		return kezhuanjiaohao_card
	end,
	enabled_at_play = function(self, player)
		return (not player:hasUsed("#kezhuanjiaohaoCard"))
	end, 
}
if not sgs.Sanguosha:getSkill("kezhuanjiaohaoex") then skills:append(kezhuanjiaohaoex) end

kezhuanjiaohao = sgs.CreateTriggerSkill{
    name = "kezhuanjiaohao",
	events = {sgs.EventPhaseStart,sgs.EventPhaseEnd},
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseEnd) then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:hasSkill("kezhuanjiaohaoex",true) then
				    room:detachSkillFromPlayer(player, "kezhuanjiaohaoex",true,true,false)
				end
			end
		end
		if (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Play) then
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do
					if p:hasSkill(self:objectName(),true) then
						room:attachSkillToPlayer(player, "kezhuanjiaohaoex")
						break
					end
				end
			end
			if (player:getPhase() == sgs.Player_Start) and player:hasSkill(self:objectName()) then
				local num = 0
				for i=0,4 do
					if player:hasEquipArea(i)
					and player:getEquip(i)==nil
					then num = num + 1 end
				end
				num = math.ceil(num/2)
				if num<1 then return end
				room:sendCompulsoryTriggerLog(player,self,math.random(1,2))
				local dummy = sgs.Sanguosha:cloneCard("slash")
				for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards(true)) do
					if sgs.Sanguosha:getEngineCard(id):isKindOf("KezhuanYing")
					and room:getCardOwner(id) == nil then
						dummy:addSubcard(id)
						if dummy:subcardsLength()>=num then break end
					end
				end
				dummy:deleteLater()
				if dummy:subcardsLength()>0 then
					player:obtainCard(dummy)
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
}
kezhuansunshuangxiang:addSkill(kezhuanjiaohao)

kezhuanhuangzhong = sgs.General(extension, "kezhuanhuangzhong", "shu", 4)

kezhuancuifengCard = sgs.CreateSkillCard{
	name = "kezhuancuifengCard" ,
	target_fixed = true ,
	mute = true,
	will_throw = false,
	about_to_use = function(self,room,use)
		--local card = sgs.Sanguosha:getCard(self:getEffectiveId())
		local choices = {}
		for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards()) do
			local tcard = sgs.Sanguosha:getEngineCard(id)
			if tcard:isDamageCard() then
				if table.contains(choices,tcard:objectName()) then continue end
				local transcard = sgs.Sanguosha:cloneCard(tcard:objectName())
				transcard:setSkillName("kezhuancuifeng")
				--transcard:addSubcard(self)
				if not transcard:isAvailable(use.from) then continue end
				if (not tcard:isKindOf("DelayedTrick")) and (tcard:isSingleTargetCard() 
				or ((use.from:aliveCount() == 2) and (tcard:isKindOf("AOE")))) then 
					table.insert(choices,tcard:objectName()) 
				end
			end
		end
		if #choices<1 then return end
		table.insert(choices,"cancel")
		local choice = room:askForChoice(use.from,"kezhuancuifeng", table.concat(choices, "+"))
		if choice=="cancel" then return end
		for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards()) do
			local c = sgs.Sanguosha:getEngineCard(id)
			if c:objectName()==choice then
				room:setPlayerMark(use.from,"kezhuancuifengName",id) 
				break 
			end
		end
		--room:setPlayerMark(use.from,"kezhuancuifengId",self:getEffectiveId())
		room:askForUseCard(use.from,"@@kezhuancuifeng","kezhuancuifeng-ask:"..choice)
	end
}
kezhuancuifengVS = sgs.CreateViewAsSkill{
	name = "kezhuancuifeng" ,
	n = 0 ,
	view_as = function(self, cards)
		if sgs.Sanguosha:getCurrentCardUsePattern()=="@@kezhuancuifeng" then
			local c = sgs.Sanguosha:getEngineCard(sgs.Self:getMark("kezhuancuifengName"))
			local transcard = sgs.Sanguosha:cloneCard(c:objectName())
			--transcard:addSubcard(sgs.Self:getMark("kezhuancuifengId"))
			transcard:setSkillName("kezhuancuifeng")
			return transcard
		elseif #cards==0
		then
			return kezhuancuifengCard:clone()
		end
	end ,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@kezhuancuifeng"
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("@kezhuancuifeng") > 0)
	end
}

kezhuancuifeng = sgs.CreateTriggerSkill{
    name = "kezhuancuifeng",
	events = {sgs.CardUsed,sgs.Damage,sgs.CardFinished,sgs.EventPhaseChanging},
	frequency = sgs.Skill_Limited,
	limit_mark = "@kezhuancuifeng",
	view_as_skill = kezhuancuifengVS,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if (use.from:objectName() == player:objectName()) then
				if (use.card:getSkillName() == "kezhuancuifeng") and player:hasSkill(self:objectName()) then
					room:removePlayerMark(player,"@kezhuancuifeng")
					room:setPlayerMark(player,"usingcuifeng",1)
				end
			end
		end
		if (event == sgs.Damage) then
			local damage = data:toDamage()
			if damage.card and (damage.card:getSkillName() == "kezhuancuifeng") then
				for _,p in sgs.qlist(room:getAllPlayers()) do
					if (p:getMark("usingcuifeng") > 0) then
				        room:addPlayerMark(p,"cuifengda",damage.damage)
						break
					end
				end
			end
		end
		if (event == sgs.CardFinished) then
			local use = data:toCardUse()
			if (use.from:objectName() == player:objectName()) then
				if (use.card:getSkillName() == "kezhuancuifeng") then
					room:setPlayerMark(player,"usingcuifeng",0)
					if (player:getMark("cuifengda") ~= 1) and player:hasSkill(self:objectName()) then
						--room:addPlayerMark(player,"@kezhuancuifeng")
						room:setPlayerMark(player,"&kezhuancuifengchongzhi",1)
					end
					room:setPlayerMark(player,"cuifengda",0)
				end
			end
		end
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) and (player:getMark("&kezhuancuifengchongzhi") > 0) then
				room:addPlayerMark(player,"@kezhuancuifeng")
				room:setPlayerMark(player,"&kezhuancuifengchongzhi",0)
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
kezhuanhuangzhong:addSkill(kezhuancuifeng)


kezhuandengnanCard = sgs.CreateSkillCard{
	name = "kezhuandengnanCard" ,
	target_fixed = true ,
	mute = true,
	will_throw = false,
	about_to_use = function(self,room,use)
		local choices = {}
		for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards()) do
			local tcard = sgs.Sanguosha:getEngineCard(id)
			if not tcard:isDamageCard() and tcard:isNDTrick()
			then
				if table.contains(choices,tcard:objectName())
				then continue end
				local transcard = sgs.Sanguosha:cloneCard(tcard:objectName())
				transcard:setSkillName("kezhuandengnan")
				if not transcard:isAvailable(use.from) then continue end
				table.insert(choices,tcard:objectName()) 
			end
		end
		if #choices<1 then return end
		table.insert(choices,"cancel")
		local choice = room:askForChoice(use.from,"kezhuandengnan", table.concat(choices, "+"))
		if choice=="cancel" then return end
		for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards()) do
			local c = sgs.Sanguosha:getEngineCard(id)
			if c:objectName()==choice then
				room:setPlayerMark(use.from,"kezhuandengnanName",id) 
				break 
			end
		end
		room:askForUseCard(use.from,"@@kezhuandengnan","kezhuandengnan-ask:"..choice)
	end
}
kezhuandengnanVS = sgs.CreateViewAsSkill{
	name = "kezhuandengnan" ,
	n = 0 ,
	view_as = function(self, cards)
		if sgs.Sanguosha:getCurrentCardUsePattern()=="@@kezhuandengnan"
		then
			local c = sgs.Sanguosha:getEngineCard(sgs.Self:getMark("kezhuandengnanName"))
			local transcard = sgs.Sanguosha:cloneCard(c:objectName())
			transcard:setSkillName("kezhuandengnan")
			return transcard
		elseif #cards==0
		then
			return kezhuandengnanCard:clone()
		end
	end ,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@kezhuandengnan"
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("@kezhuandengnan") > 0)
	end
}

kezhuandengnan = sgs.CreateTriggerSkill{
    name = "kezhuandengnan",
	events = {sgs.TargetSpecified,sgs.Damaged,sgs.CardUsed,sgs.EventPhaseChanging},
	frequency = sgs.Skill_Limited,
	limit_mark = "@kezhuandengnan",
	view_as_skill = kezhuandengnanVS,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if use.card:getSkillName() == "kezhuandengnan" and player:hasSkill(self:objectName()) then
				room:removePlayerMark(player,"@kezhuandengnan")
			end
		end
		if (event == sgs.TargetSpecified) then
			local use = data:toCardUse()
			if (use.card:getSkillName() == "kezhuandengnan") and use.from:hasSkill(self:objectName()) then
				room:setPlayerMark(use.from,"usingdengnan-Clear",1)
				for _,p in sgs.qlist(use.to) do
					if (p:getMark("&kezhuandengnanover") == 0) then
						if (p:getMark("&kezhuandengnanda") > 0) then
							room:setPlayerMark(p,"&kezhuandengnanda",0)
							room:setPlayerMark(p,"&kezhuandengnanover",1)
						else
							room:setPlayerMark(p,"&kezhuandengnantar",1)
						end
					end
				end
			end
		end
		if (event == sgs.Damaged) then
			local damage = data:toDamage()
			local hz = room:findPlayerBySkillName(self:objectName())
			if hz and (hz:getPhase() ~= sgs.Player_NotActive) then
				if (damage.to:getMark("&kezhuandengnanover") == 0) then
					if (damage.to:getMark("&kezhuandengnantar") > 0) then
						room:setPlayerMark(damage.to,"&kezhuandengnantar",0)
						room:setPlayerMark(damage.to,"&kezhuandengnanover",1)
					else
						room:setPlayerMark(damage.to,"&kezhuandengnanda",1)
					end
				end
			end
		end
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) then
				if (player:getMark("usingdengnan-Clear") > 0) then
					local yes = 1
					for _,p in sgs.qlist(room:getAllPlayers()) do
						if (p:getMark("&kezhuandengnantar") > 0) and (p:getMark("&kezhuandengnanda") == 0) then
							yes = 0
							break
						end
					end
					if (yes == 1) then
						room:addPlayerMark(player,"@kezhuandengnan")
					end
				end
				for _,pp in sgs.qlist(room:getAllPlayers()) do
					room:setPlayerMark(pp,"&kezhuandengnantar",0)
					room:setPlayerMark(pp,"&kezhuandengnanover",0)
					room:setPlayerMark(pp,"&kezhuandengnanda",0)
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
kezhuanhuangzhong:addSkill(kezhuandengnan)


kezhuanpangtong = sgs.General(extension, "kezhuanpangtong", "qun", 3)


kezhuanmanjuanVsCard = sgs.CreateSkillCard{
	name = "kezhuanmanjuanVsCard",
	filter = function(self,targets,to_select,from)
		local pattern = self:getUserString()
		if pattern=="@@kezhuanmanjuan" then
			local c = sgs.Sanguosha:getCard(from:getMark("kezhuanmanjuan_id"))
			if c:targetFixed() then return false end
			local plist = sgs.PlayerList()
			for i = 1,#targets do plist:append(targets[i]) end
			return c:targetFilter(plist,to_select,from)
		else
			if sgs.Sanguosha:getCurrentCardUseReason()~=sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then return false end
			for i=0,sgs.Sanguosha:getCardCount()-1 do
				local c = sgs.Sanguosha:getEngineCard(i)
				if from:getMark(i.."manjuanPile-Clear")>0
				and from:getMark(c:getNumber().."manjuanNumber-Clear")<1
				and not from:isLocked(c) then
					local pn = c:isKindOf("Slash") and "slash" or c:objectName()
					if string.find(pattern,pn) then
						if c:targetFixed() then return false end
						local plist = sgs.PlayerList()
						for i = 1,#targets do plist:append(targets[i]) end
						return c:targetFilter(plist,to_select,from)
					end
				end
			end
		end
	end,
	feasible = function(self,targets,from)
		local pattern = self:getUserString()
		if pattern=="@@kezhuanmanjuan" then
			local c = sgs.Sanguosha:getCard(sgs.Self:getMark("kezhuanmanjuan_id"))
			if c:targetFixed() then return true end
			local plist = sgs.PlayerList()
			for i = 1,#targets do plist:append(targets[i]) end
			return c:targetsFeasible(plist,from)
		else
			if sgs.Sanguosha:getCurrentCardUseReason()~=sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then return true end
			for i=0,sgs.Sanguosha:getCardCount()-1 do
				local c = sgs.Sanguosha:getEngineCard(i)
				if from:getMark(i.."manjuanPile-Clear")>0
				and from:getMark(c:getNumber().."manjuanNumber-Clear")<1
				and not from:isLocked(c) then
					local pn = c:isKindOf("Slash") and "slash" or c:objectName()
					if string.find(pattern,pn) then
						if c:targetFixed() then return true end
						local plist = sgs.PlayerList()
						for i = 1,#targets do plist:append(targets[i]) end
						return c:targetsFeasible(plist,from)
					end
				end
			end
		end
	end,
	on_validate = function(self,use)
		local room = use.from:getRoom()
		local pattern = self:getUserString()
		if pattern=="@@kezhuanmanjuan" then
			local c = sgs.Sanguosha:getCard(use.from:getMark("kezhuanmanjuan_id"))
			room:broadcastSkillInvoke("kezhuanmanjuan")--播放配音
			room:setCardFlag(c,"kezhuanmanjuan")
			return c
		else
			local ids = sgs.IntList()
			for i=0,sgs.Sanguosha:getCardCount()-1 do
				local c = sgs.Sanguosha:getEngineCard(i)
				if use.from:getMark(i.."manjuanPile-Clear")>0
				and use.from:getMark(c:getNumber().."manjuanNumber-Clear")<1
				and not use.from:isCardLimited(c,sgs.Card_MethodUse) then
					local pn = c:isKindOf("Slash") and "slash" or c:objectName()
					if string.find(pattern,pn) and (use.from:canUse(c,use.to) or c:targetFixed())
					then ids:append(i) end
				end
			end
			room:fillAG(ids,use.from)
			local c = room:askForAG(use.from,ids,ids:length()<2,"kezhuanmanjuan","kezhuanmanjuan0")
			room:clearAG(use.from)
			c = c<0 and ids:at(0) or c
			c = sgs.Sanguosha:getCard(c)
			room:broadcastSkillInvoke("kezhuanmanjuan")--播放配音
			room:setCardFlag(c,"kezhuanmanjuan")
			return c
		end
	end,
	on_validate_in_response = function(self,from)
		local room = from:getRoom()
		local pattern = self:getUserString()
		if pattern=="@@kezhuanmanjuan" then
			local c = sgs.Sanguosha:getCard(from:getMark("kezhuanmanjuan_id"))
			room:broadcastSkillInvoke("kezhuanmanjuan")--播放配音
			room:setCardFlag(c,"kezhuanmanjuan")
			return c
		else
			local hm = sgs.Sanguosha:getCurrentCardUseReason()==sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
			hm = hm and sgs.Card_MethodUse or sgs.Card_MethodResponse
			local ids = sgs.IntList()
			for i=0,sgs.Sanguosha:getCardCount()-1 do
				local c = sgs.Sanguosha:getCard(i)
				if from:getMark(i.."manjuanPile-Clear")>0
				and from:getMark(c:getNumber().."manjuanNumber-Clear")<1
				and not from:isCardLimited(c,hm) then
					local pn = c:isKindOf("Slash") and "slash" or c:objectName()
					if string.find(pattern,pn) then ids:append(i) end
				end
			end
			room:fillAG(ids,from)
			local c = room:askForAG(from,ids,ids:length()<2,"kezhuanmanjuan","kezhuanmanjuan1")
			room:clearAG(from)
			c = c<0 and ids:at(0) or c
			c = sgs.Sanguosha:getCard(c)
			room:broadcastSkillInvoke("kezhuanmanjuan")--播放配音
			room:setCardFlag(c,"kezhuanmanjuan")
			return c
		end
	end
}

kezhuanmanjuanCard = sgs.CreateSkillCard{
	name = "kezhuanmanjuanCard",
	target_fixed = true,
	about_to_use = function(self,room,use)
		local ids = sgs.IntList()
		for i=0,sgs.Sanguosha:getCardCount()-1 do
			local c = sgs.Sanguosha:getCard(i)
			if use.from:getMark(i.."manjuanPile-Clear")>0
			and use.from:getMark(c:getNumber().."manjuanNumber-Clear")<1
			and c:isAvailable(use.from) then ids:append(i) end
		end
		room:fillAG(ids,use.from)
		local id = room:askForAG(use.from,ids,ids:length()<2,"kezhuanmanjuan","kezhuanmanjuan0")
		id = id<0 and ids:at(0) or id
		room:clearAG(use.from)
		room:setPlayerMark(use.from,"kezhuanmanjuan_id",id)
		room:askForUseCard(use.from,"@@kezhuanmanjuan","kezhuanmanjuan2:"..sgs.Sanguosha:getCard(id):objectName())
	end
}
kezhuanmanjuanvs = sgs.CreateViewAsSkill{
	name = "kezhuanmanjuan",
	view_as = function(self,cards)
		if sgs.Sanguosha:getCurrentCardUseReason()==sgs.CardUseStruct_CARD_USE_REASON_PLAY
		then return kezhuanmanjuanCard:clone()
		else
			local c = kezhuanmanjuanVsCard:clone()
			c:setUserString(sgs.Sanguosha:getCurrentCardUsePattern())
			return c
		end
	end,
	enabled_at_response = function(self,player,pattern)
	   	if pattern=="@@kezhuanmanjuan" then return true
		elseif player:getHandcardNum()>0 then return false end
		local hm = sgs.Sanguosha:getCurrentCardUseReason()==sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
		hm = hm and sgs.Card_MethodUse or sgs.Card_MethodResponse
		for i=0,sgs.Sanguosha:getCardCount()-1 do
			local c = sgs.Sanguosha:getEngineCard(i)
			if player:getMark(i.."manjuanPile-Clear")>0
			and player:getMark(c:getNumber().."manjuanNumber-Clear")<1
			and not player:isCardLimited(c,hm) then
				c = c:isKindOf("Slash") and "slash" or c:objectName()
				if string.find(pattern,c) then return true end
			end
		end
	end,
	enabled_at_nullification = function(self,player)				
		if player:getHandcardNum()>0 then return end
		for i=0,sgs.Sanguosha:getCardCount()-1 do
			local c = sgs.Sanguosha:getEngineCard(i)
			if player:getMark(i.."manjuanPile-Clear")>0
			and player:getMark(c:getNumber().."manjuanNumber-Clear")<1
			and not player:isLocked(c) and c:isKindOf("Nullification")
			then return true end
		end
	end,
	enabled_at_play = function(self,player)
		if player:getHandcardNum()>0 then return end
		for i=0,sgs.Sanguosha:getCardCount()-1 do
			local c = sgs.Sanguosha:getEngineCard(i)
			if player:getMark(i.."manjuanPile-Clear")>0
			and player:getMark(c:getNumber().."manjuanNumber-Clear")<1
			and c:isAvailable(player) then return true end
		end
	end
}
kezhuanmanjuan = sgs.CreateTriggerSkill{
	name = "kezhuanmanjuan",
	events = {sgs.CardsMoveOneTime,sgs.PreCardUsed,sgs.PreCardResponded,sgs.SwappedPile},
	view_as_skill = kezhuanmanjuanvs,
	on_trigger = function(self,event,player,data,room)
		if event==sgs.CardsMoveOneTime then
	     	local move = data:toMoveOneTime()
			if move.to_place==sgs.Player_DiscardPile then
				for _,id in sgs.list(move.card_ids)do
					if room:getCardPlace(id)~=sgs.Player_DiscardPile then continue end
					room:addPlayerMark(player,id.."manjuanPile-Clear")
				end
			elseif move.from_places:contains(sgs.Player_DiscardPile) then
				for _,id in sgs.list(move.card_ids)do
					room:setPlayerMark(player,id.."manjuanPile-Clear",0)
				end
			end
		elseif event==sgs.SwappedPile then
			for _,m in sgs.list(player:getMarkNames()) do
				if m:endsWith("manjuanPile-Clear") then
					room:setPlayerMark(player,m,0)
				end
			end
		else
			local card
			if event==sgs.PreCardResponded then
				local res = data:toCardResponse()
				if res.m_isUse then card = res.m_card end
			else
				card = data:toCardUse().card
			end
			if card and card:hasFlag("kezhuanmanjuan") then
				room:setCardFlag(card,"-kezhuanmanjuan")
				room:addPlayerMark(player,card:getNumber().."manjuanNumber-Clear")
			end
		end
		return false
	end
}

kezhuanpangtong:addSkill(kezhuanmanjuan)

kezhuanyangmingCard = sgs.CreateSkillCard{
	name = "kezhuanyangmingCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		return #targets == 0 and (to_select:objectName() ~= player:objectName())
		and (player:canPindian(to_select, true))
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		while player:isAlive() and target:isAlive() and player:canPindian(target) do
			if player:pindian(target, self:getSkillName()) then
				if player:isAlive() and target:isAlive() and player:canPindian(target)
				and player:askForSkillInvoke(self:getSkillName(),KezhuanToData("kezhuanyangming-jixu:"..target:objectName()))
				then else break end
			else
				if target:isAlive() then
					target:drawCards(target:getMark("&kezhuanyangminglose-PlayClear"),self:getSkillName())
				end
				if player:isAlive() then
					room:recover(player, sgs.RecoverStruct(self:getSkillName(),player))
				end
				break
			end
		end
	end
}

kezhuanyangmingVS = sgs.CreateViewAsSkill{
	name = "kezhuanyangming",
	n = 0,
	view_as = function(self, cards)
		return kezhuanyangmingCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not (player:hasUsed("#kezhuanyangmingCard")) 
	end, 
}

kezhuanyangming = sgs.CreateTriggerSkill{
	name = "kezhuanyangming" ,
	view_as_skill = kezhuanyangmingVS,
	events = {sgs.Pindian} ,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Pindian) then
			local cp = room:getCurrent()
			if cp and cp:hasSkill(self,true) and cp:getPhase()==sgs.Player_Play then
				local pindian = data:toPindian()
				--不管是因为什么拼点，给输的人标记
				if pindian.success then
					room:addPlayerMark(pindian.to,"&kezhuanyangminglose-PlayClear")
				else
					room:addPlayerMark(pindian.from,"&kezhuanyangminglose-PlayClear")
				end
			end
		end
	end
}
kezhuanpangtong:addSkill(kezhuanyangming)


kezhuanlougui = sgs.General(extension, "kezhuanlougui", "wei", 3,true)

kezhuanshacheng = sgs.CreateTriggerSkill{
    name = "kezhuanshacheng",
	events = {sgs.GameStart,sgs.CardFinished,sgs.CardsMoveOneTime},
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if (move.from_places:contains(sgs.Player_PlaceHand) or move.from_places:contains(sgs.Player_PlaceEquip))
			and move.from:objectName() == player:objectName()
			and not(move.to and (move.to:objectName() == player:objectName() 
			and (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip))) then
				room:addPlayerMark(player,"kezhuanshachenglose-Clear",move.card_ids:length())
			end
		end
		if (event == sgs.GameStart) and player:hasSkill(self:objectName()) then
			room:sendCompulsoryTriggerLog(player,self)
			local sc_card = room:getNCards(2)
			player:addToPile("kezhuanshacheng", sc_card)
		end
		if (event == sgs.CardFinished) then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				for _,lg in sgs.qlist(room:getAllPlayers()) do
					if lg:hasSkill(self:objectName()) then
						local ids = lg:getPile("kezhuanshacheng")
						if ids:isEmpty() then continue end
						local tos = sgs.SPlayerList()
						for _,p in sgs.qlist(use.to) do
							if p:isAlive() then
								tos:append(p)
							end
						end
						local fri = room:askForPlayerChosen(lg, tos, self:objectName(), "kezhuanshacheng-ask",true,true)
						if fri then
							room:broadcastSkillInvoke(self:objectName())
							room:fillAG(ids, lg)
							local id = room:askForAG(lg, lg:getPile("kezhuanshacheng"), false, self:objectName())
							room:clearAG(lg)
							room:throwCard(id, lg)
							local num = fri:getMark("kezhuanshachenglose-Clear")
							fri:drawCards(math.min(5,num),self:objectName())
						end	
					end
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
}
kezhuanlougui:addSkill(kezhuanshacheng)

kezhuanninghan = sgs.CreateTriggerSkill{
    name = "kezhuanninghan",
	events = {sgs.Damaged,sgs.GameStart,sgs.Death,sgs.EventAcquireSkill,sgs.EventLoseSkill},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.Damaged) then
			local damage = data:toDamage()
			if damage.card and (damage.nature == sgs.DamageStruct_Ice) then
				for _,p in sgs.qlist(room:getAllPlayers()) do
					if room:getCardPlace(damage.card:getEffectiveId()) == sgs.Player_PlaceTable
					and p:hasSkill(self:objectName()) then
						if p:askForSkillInvoke(self,KezhuanToData("kezhuanninghan-ask:"..damage.card:objectName())) then
							room:broadcastSkillInvoke(self:objectName())
							p:addToPile("kezhuanshacheng", damage.card)
							break	  
						end
					end
				end
			end
		end
		if (event == sgs.GameStart or event == sgs.EventAcquireSkill) and player:hasSkill(self,true) then
			for _,p in sgs.qlist(room:getAllPlayers()) do
				if not p:hasSkill("kezhuanninghanbuff") then
					room:attachSkillToPlayer(p, "kezhuanninghanbuff")
				end
			end
		end
		if (event == sgs.Death) then
			local death = data:toDeath()
			if death.who == player and player:hasSkill(self,true) then
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do 
					if p:hasSkill(self,true) then
						return false
					end
				end
				for _,p in sgs.qlist(room:getAllPlayers()) do
					room:detachSkillFromPlayer(p, "kezhuanninghanbuff",true,true,false)
				end
			end
		end
		if (event == sgs.EventLoseSkill) and data:toString() == "kezhuanninghan" then
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do 
				if p:hasSkill(self,true) then
					return false
				end
			end
			for _,p in sgs.qlist(room:getAllPlayers()) do
				room:detachSkillFromPlayer(p, "kezhuanninghanbuff",true,true,false)
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
kezhuanlougui:addSkill(kezhuanninghan)

kezhuanninghanbuff = sgs.CreateFilterSkill{
	name = "kezhuanninghanbuff&",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		return room:getCardPlace(to_select:getEffectiveId()) == sgs.Player_PlaceHand
		and to_select:getSuit() == sgs.Card_Club and to_select:isKindOf("Slash")
	end,
	view_as = function(self, originalCard)
		local slash = sgs.Sanguosha:cloneCard("ice_slash", originalCard:getSuit(), originalCard:getNumber())
		slash:setSkillName("kezhuanninghan")
		local card = sgs.Sanguosha:getWrappedCard(originalCard:getId())
		card:takeOver(slash)
		return card
	end
}
if not sgs.Sanguosha:getSkill("kezhuanninghanbuff") then skills:append(kezhuanninghanbuff) end


kezhuanhansui = sgs.General(extension, "kezhuanhansui$", "qun", 4,true)

kezhuanniluanCard = sgs.CreateSkillCard{
	name = "kezhuanniluanCard",
	will_throw = true,
	filter = function(self, targets, to_select, player)
		return self:subcardsLength()~=to_select:getMark("kezhuanniluan"..player:objectName())
	end,
	on_use = function(self, room, source, targets)
		for _, p in ipairs(targets)do
			if (self:subcardsLength() == 0) then
				p:drawCards(2,self:getSkillName())
			else
				room:damage(sgs.DamageStruct(self:getSkillName(), source, p))
			end
		end
	end,
}

kezhuanniluanVS = sgs.CreateViewAsSkill{
	name = "kezhuanniluan",
	n = 1 ,
	view_filter = function(self, selected, to_select)
		return not sgs.Self:isJilei(to_select)
	end ,
	view_as = function(self, cards)
		local dc = kezhuanniluanCard:clone()
		for _, c in ipairs(cards) do
			dc:addSubcard(c)
		end
		return dc
	end ,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@kezhuanniluan"
	end,
	enabled_at_play = function(self, player)
		return false
	end
}

kezhuanniluan = sgs.CreateTriggerSkill{
    name = "kezhuanniluan",
	events = {sgs.EventPhaseStart,sgs.Damage,sgs.Damaged},
	view_as_skill = kezhuanniluanVS,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.Damaged) then
			local damage = data:toDamage()
			room:setPlayerMark(player,"kezhuanniluan"..damage.from:objectName(),1)
		end
		if (event == sgs.EventPhaseStart) and (player:getPhase() == sgs.Player_Start) then
			room:askForUseCard(player,"@@kezhuanniluan","kezhuanniluan-ask")
		end
	end,
}
kezhuanhansui:addSkill(kezhuanniluan)

kezhuanhuchou = sgs.CreateTriggerSkill{
    name = "kezhuanhuchou",
	events = {sgs.CardUsed,sgs.ConfirmDamage},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.ConfirmDamage) then
			local damage = data:toDamage()
			if damage.from:hasSkill(self) and damage.to:getMark("&kezhuanhuchou+#"..damage.from:objectName())>0 then
				room:sendCompulsoryTriggerLog(damage.from,self)
				damage.damage = damage.damage + 1
				data:setValue(damage)
			end
		end
		if (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if use.card:isDamageCard() then
				for _, p in sgs.qlist(use.to) do 
					if p:hasSkill(self:objectName()) then
						for _, ap in sgs.qlist(room:getAllPlayers()) do 
							room:setPlayerMark(ap,"&kezhuanhuchou+#"..p:objectName(),0)
						end
						room:setPlayerMark(use.from,"&kezhuanhuchou+#"..p:objectName(),1)
					end
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end
}
kezhuanhansui:addSkill(kezhuanhuchou)

kezhuanjiemeng = sgs.CreateDistanceSkill{
	name = "kezhuanjiemeng$",
	correct_func = function(self, from)
		if from:getKingdom() ~= "qun"
		then return 0 end
		local yes,num = false,0
		local tos = from:getAliveSiblings()
		tos:append(from)
		for _, p in sgs.qlist(tos) do 
			if p:getKingdom() == "qun" then num = num-1 end
			if yes==false and p:hasLordSkill(self:objectName())
			then yes = true end
		end
		return yes and num or 0
	end,
}
kezhuanhansui:addSkill(kezhuanjiemeng)



kezhuanzhangchu = sgs.General(extension, "kezhuanzhangchu", "qun", 3,false)

kezhuanhuozhongCard = sgs.CreateSkillCard{
	name = "kezhuanhuozhongCard",
	will_throw = false,
	--mute = true,
	filter = function(self, targets, to_select, source)
		local shortage = sgs.Sanguosha:cloneCard("supply_shortage")
		shortage:setSkillName(self:getSkillName()) 
		shortage:addSubcard(self)
		shortage:deleteLater()
		return not to_select:containsTrick("supply_shortage")
		and to_select:objectName()==source:objectName() and to_select:hasJudgeArea()
		and not source:isProhibited(to_select, shortage)
	end,
	on_use = function(self, room, source, targets)
		local target = targets[1]
		local supplyshortage = sgs.Sanguosha:cloneCard("supply_shortage")
		supplyshortage:setSkillName(self:getSkillName()) 
		supplyshortage:addSubcard(self)
		local tos = sgs.SPlayerList()
		tos:append(target)
        room:moveCardTo(supplyshortage, nil, sgs.Player_PlaceTable, true)
		supplyshortage:use(room,source,tos)
		supplyshortage:deleteLater()
		tos = room:findPlayersBySkillName(self:getSkillName())
		local zc = room:askForPlayerChosen(source, tos, self:getSkillName(), "kezhuanhuozhong-choose", false, false)
		if zc then
			zc:drawCards(2,self:getSkillName())
		end
	end,
}

kezhuanhuozhongVS = sgs.CreateViewAsSkill{
	name = "kezhuanhuozhong",
	n = 1,
	view_filter = function(self, selected, to_select)
		return (to_select:isBlack() and not to_select:isKindOf("TrickCard"))
	end ,
	view_as = function(self, cards)
        if #cards == 1 then
			local card = kezhuanhuozhongCard:clone()
			card:addSubcard(cards[1])
			return card
		end
	end,
	enabled_at_play = function(self, player)
		local tos = player:getAliveSiblings()
		tos:append(player)
		for _, p in sgs.qlist(tos) do
			if p:hasSkill("kezhuanhuozhong") then
				return not player:hasUsed("#kezhuanhuozhongCard")
			end
		end
	end, 
}
kezhuanhuozhongex = sgs.CreateViewAsSkill{
	name = "kezhuanhuozhongex&",
	n = 1,
	view_filter = function(self, selected, to_select)
		return (to_select:isBlack() and not to_select:isKindOf("TrickCard"))
	end ,
	view_as = function(self, cards)
        if #cards == 1 then
			local card = kezhuanhuozhongCard:clone()
			card:addSubcard(cards[1])
			return card
		end
	end,
	enabled_at_play = function(self, player)
		local tos = player:getAliveSiblings()
		tos:append(player)
		for _, p in sgs.qlist(tos) do
			if p:hasSkill("kezhuanhuozhong") then
				return not player:hasUsed("#kezhuanhuozhongCard")
			end
		end
	end, 
}
if not sgs.Sanguosha:getSkill("kezhuanhuozhongex") then skills:append(kezhuanhuozhongex) end

kezhuanhuozhong = sgs.CreateTriggerSkill{
    name = "kezhuanhuozhong",
	events = {sgs.EventPhaseStart,sgs.EventPhaseEnd},
	view_as_skill = kezhuanhuozhongVS,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseEnd) then
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if p:hasSkill("kezhuanhuozhongex",true) then
				    room:detachSkillFromPlayer(player, "kezhuanhuozhongex",true,true,false)
				end
			end
		end
		if (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Play) then
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do
					if p:hasSkill(self:objectName(),true) then
						room:attachSkillToPlayer(player, "kezhuanhuozhongex")
						break
					end
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
kezhuanzhangchu:addSkill(kezhuanhuozhong)


kezhuanrihui = sgs.CreateTriggerSkill{
    name = "kezhuanrihui",
	events = {sgs.Damage},
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.Damage) then
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("Slash") then 
				local use = room:getUseStruct(damage.card)
				if use.to:contains(damage.to)
				and player:askForSkillInvoke(self,KezhuanToData("kezhuanrihui")) then
					room:broadcastSkillInvoke(self:objectName())
					for _, p in sgs.qlist(room:getOtherPlayers(player))do
						if p:getJudgingArea():length()>0 then
							p:drawCards(1,self:objectName())
						end
					end
				end
			end
		end
	end,
}
kezhuanzhangchu:addSkill(kezhuanrihui)


kezhuanxiahouen = sgs.General(extension, "kezhuanxiahouen", "wei", 4)

kezhuanchixueqingfengskill = sgs.CreateTriggerSkill{
	name = "_kezhuan_chixueqingfeng",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TargetConfirming, sgs.CardFinished},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if event == sgs.TargetConfirming then
			if use.card:isKindOf("Slash") and use.from:hasWeapon(self:objectName()) then
				room:sendCompulsoryTriggerLog(use.from, self:objectName())
				room:setEmotion(use.from, "weapon/qinggang_sword")
				for _, p in sgs.qlist(use.to) do
					room:setPlayerCardLimitation(p, "use,response", ".|.|.|hand", false)
					p:addQinggangTag(use.card)
					p:setFlags("kezhuan_cxqfto")
				end
				data:setValue(use)
			end
		elseif event == sgs.CardFinished and use.card:isKindOf("Slash") then
			for _, p in sgs.qlist(room:getAllPlayers())do
				if p:hasFlag("kezhuan_cxqfto") then
					p:setFlags("-kezhuan_cxqfto")
					room:removePlayerCardLimitation(p, "use,response", ".|.|.|hand")
					p:removeQinggangTag(use.card)
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
extension:addSkills(kezhuanchixueqingfengskill)
Kezhuan_chixueqingfeng = sgs.CreateWeapon{
	name = "_kezhuan_chixueqingfeng",
	class_name = "Kezhuan_chixueqingfeng",
	range = 2,
	on_install = function(self, player)
		local room = player:getRoom()
		room:acquireSkill(player,kezhuanchixueqingfengskill,true,true,false)
	end,
	on_uninstall = function(self, player)
		local room = player:getRoom()
		room:detachSkillFromPlayer(player,"kezhuanchixueqingfengskill",true,true)
	end,
}

Kezhuan_chixueqingfeng:clone(sgs.Card_Spade, 6):setParent(extension)

kezhuanhujian = sgs.CreateTriggerSkill{
    name = "kezhuanhujian",
	waked_skills = "kezhuanchixueqingfengskill",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseChanging,sgs.GameStart,sgs.CardResponded,sgs.CardUsed},
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.GameStart) and player:hasSkill(self:objectName()) then
			local cards = sgs.IntList()
			for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards(true)) do
				if sgs.Sanguosha:getEngineCard(id):isKindOf("Kezhuan_chixueqingfeng") and (room:getCardPlace(id) ~= sgs.Player_DrawPile)
				and (room:getCardPlace(id) ~= sgs.Player_PlaceHand) and (room:getCardPlace(id) ~= sgs.Player_PlaceEquip)
				and (room:getCardPlace(id) ~= sgs.Player_DiscardPile) then
					cards:append(id)
					break
				end
			end
			if not cards:isEmpty() then
				local thecard = sgs.Sanguosha:getCard(cards:at(0))
				room:sendCompulsoryTriggerLog(player,self,1)
				player:obtainCard(thecard)
			end		
		end
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) then
				for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards(true)) do
					if sgs.Sanguosha:getEngineCard(id):isKindOf("Kezhuan_chixueqingfeng") 
					and (room:getCardPlace(id) == sgs.Player_DiscardPile) then
						for _, p in sgs.qlist(room:getAllPlayers()) do
							if (p:getMark("&kezhuanhujian-Clear") > 0) then
								if p:askForSkillInvoke(self:objectName(),KezhuanToData("kezhuanhujian-ask:"..p:objectName())) then
									room:broadcastSkillInvoke(self:objectName(),2)
									p:obtainCard(sgs.Sanguosha:getCard(id))
									break
								end
							end
						end
						break
					end
				end
			end
		end
		if (event == sgs.CardResponded) then
			local response = data:toCardResponse()
			if response.m_card:getTypeId()<1 then return end
			for _, p in sgs.qlist(room:getAllPlayers()) do
				room:setPlayerMark(p,"&kezhuanhujian-Clear",0)
			end
			room:setPlayerMark(player,"&kezhuanhujian-Clear",1)
		end
		if (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if use.card:getTypeId()<1 then return end
			for _, p in sgs.qlist(room:getAllPlayers()) do
				room:setPlayerMark(p,"&kezhuanhujian-Clear",0)
			end
			room:setPlayerMark(player,"&kezhuanhujian-Clear",1)
		end
	end,
}
kezhuanxiahouen:addSkill(kezhuanhujian)

kezhuanshiliVS = sgs.CreateOneCardViewAsSkill{
	name = "kezhuanshili",
	response_or_use = true,
	view_filter = function(self, card)
		return (not card:isEquipped()) and card:isKindOf("EquipCard")
	end,
	view_as = function(self, card)
		local duel = sgs.Sanguosha:cloneCard("duel")
		duel:addSubcard(card)
		duel:setSkillName("kezhuanshili")
		return duel
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("usekezhuanshili-PlayClear") == 0)
	end, 
}

kezhuanshili = sgs.CreateTriggerSkill{
    name = "kezhuanshili",
	events = {sgs.CardUsed},
	view_as_skill = kezhuanshiliVS,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if (use.card:getSkillName() == "kezhuanshili") then
				if (player:objectName() == use.from:objectName()) then
				    room:setPlayerMark(player,"usekezhuanshili-PlayClear",1)
				end
			end
		end
	end,
}
kezhuanxiahouen:addSkill(kezhuanshili)




kezhuanfanjiangzhangda = sgs.General(extension, "kezhuanfanjiangzhangda", "wu", 5)

kezhuanfushan = sgs.CreateTriggerSkill{
    name = "kezhuanfushan",
	frequency = sgs.Skill_Frequent,
	events = {sgs.EventPhaseStart,sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Play) then
				for _,p in sgs.qlist(room:getOtherPlayers(player)) do
					local card = room:askForExchange(p, self:objectName(), 1, 0, true, "kezhuanfushangive:"..player:objectName(),true)
					if card then
						room:addPlayerMark(p,"&kezhuanfushan-PlayClear")
						room:addPlayerMark(player,"kezhuanfushannum-PlayClear")
						room:obtainCard(player, card, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, player:objectName(), player:objectName(), self:objectName(), ""), false)
						room:addSlashCishu(player,1, true)
					end
				end	
			end
		end
		if (event == sgs.EventPhaseEnd) then
			if (player:getPhase() == sgs.Player_Play) then
				local willlose = 0
				if sgs.Slash_IsAvailable(player) then
					willlose = willlose + 1
				end
				local num = 0
				for _,p in sgs.qlist(room:getAllPlayers()) do
					if p:getMark("&kezhuanfushan-PlayClear") > 0 then
						num = num + 1
					end
				end
				if (num == player:getMark("kezhuanfushannum-PlayClear")) and (num ~= 0) then
					willlose = willlose + 1
				end	
				if (willlose == 2) then
					room:sendCompulsoryTriggerLog(player,self)
					room:loseHp(player,2)
				else
					local cha = player:getMaxHp() - player:getHandcardNum() 
					if (cha > 0) then
						room:sendCompulsoryTriggerLog(player,self)
						player:drawCards(cha,self:objectName())
					end
				end
			end
		end
	end,
}
kezhuanfanjiangzhangda:addSkill(kezhuanfushan)

















kezhuancaimaozhangyun = sgs.General(extension, "kezhuancaimaozhangyun", "wei", 4,true,true)
kezhuancaimaozhangyun:addSkill("lianzhou")
kezhuancaimaozhangyun:addSkill("jinglan")

kezhuanjianggan = sgs.General(extension, "kezhuanjianggan", "wei", 3,true,true)
kezhuanjianggan:addSkill("weicheng")
kezhuanjianggan:addSkill("daoshu")

kezhuanhuangchengyan = sgs.General(extension, "kezhuanhuangchengyan", "qun", 3,true,true)
kezhuanhuangchengyan:addSkill("guanxu")
kezhuanhuangchengyan:addSkill("yashi")

kezhuankanze = sgs.General(extension, "kezhuankanze", "wu", 3,true,true)
kezhuankanze:addSkill("xiashu")
kezhuankanze:addSkill("tenyearkuanshi")



sgs.Sanguosha:addSkills(skills)
sgs.LoadTranslationTable{
    ["kearjsrgszhuan"] = "江山如故·转",

	["KezhuanYing"] = "影",
	["_kezhuan_ying"] = "影",
	["kespecial_card"] = "特殊牌",

	[":_kezhuan_ying"] = "基本牌<br /><b>时机</b>：无<br /><b>效果</b>：无",

	["_kezhuan_chixueqingfeng"] = "赤血青锋", 
	[":_kezhuan_chixueqingfeng"] = "装备牌/武器<br /><b>攻击范围</b>：２\
	<b>武器技能</b>：锁定技，你使用的【杀】结算结束前，目标角色不能使用或打出手牌，且此【杀】无视其防具。",

	--郭嘉
	["kezhuanguojia"] = "郭嘉[转]", 
	["&kezhuanguojia"] = "郭嘉",
	["#kezhuanguojia"] = "赤壁的先知",
	["designer:kezhuanguojia"] = "官方",
	["cv:kezhuanguojia"] = "官方",
	["illustrator:kezhuanguojia"] = "Kayak&DEEMO",
	["information:kezhuanguojia"] = "ᅟᅠ<i>初平元年二月，郭嘉拜见袁绍，曹操怒斥众诸侯，乃对曰：董卓于汴水或有埋伏，慎之！曹操未从，果败于徐荣。三月，曹操与郭嘉论天下事：使孤成大业者，必此人也。\
	ᅟᅠ郭嘉从破袁绍，讨谭、尚，连战连克，计定辽东。时年三十八，征乌桓归途郭嘉因劳染疾，命悬之际竟意外饮下柳皮醋水而愈。建安十三年，曹操屯兵赤壁，郭嘉识破连环之计，设上中下三策，可胜刘备。尚未献策，曹操便决意采纳上策：“奉孝之才，足胜孤百倍，卿言上策，如何不取？”由此，赤壁战后曹操尽得天下。</i>",


	["kezhuanqingzi"] = "轻辎",
	[":kezhuanqingzi"] = "<font color='green'><b>准备阶段，</s></font>你可以弃置任意名其他角色装备区里的各一张牌，然后这些角色获得“神速”直到你下回合开始。",

	["kezhuandingce"] = "定策",
	[":kezhuandingce"] = "当你受到伤害后，你可以弃置你和伤害来源的各一张手牌，若这两张牌颜色相同，你视为使用一张【洞烛先机】。",

	["kezhuanzhenfeng"] = "针锋",
	[":kezhuanzhenfeng"] = "<font color='green'><b>出牌阶段每种类型限一次，</s></font>你可以视为使用一张存活角色的技能描述中包含的基本牌或普通锦囊牌（无次数和距离限制），当此牌对一名技能描述中包含此牌名的角色生效后，你对其造成1点伤害。",

	["kezhuanzhenfeng1"] = "你可以视为使用【%src】",
	["kezhuanqingzi-ask"] = "你可以选择发动“轻辎”的角色",
	["kezhuandingce-discard"] = "请选择发动“定策”弃置的牌",

	["$kezhuanqingzi1"] = "下一步棋，我已经计划好了。",
	["$kezhuanqingzi2"] = "我已有所顿悟。",
	["$kezhuandingce1"] = "一身才落拓，狂慧藐凡尘。",
	["$kezhuandingce2"] = "平生多偃蹇，何幸得天恩。",
	["$kezhuanzhenfeng1"] = "深感情之切，策谋以报君！",
	["$kezhuanzhenfeng2"] = "此笺相寄予，数语以销魂。",

	["~kezhuanguojia"] = "奉孝将去，主公保重。",


	--马超
	["kezhuanmachao"] = "马超[转]", 
	["&kezhuanmachao"] = "马超",
	["#kezhuanmachao"] = "潼关之勇",
	["designer:kezhuanmachao"] = "官方",
	["cv:kezhuanmachao"] = "官方",
	["illustrator:kezhuanmachao"] = "鬼画府",

	["kezhuanzhuiming"] = "追命",
	[":kezhuanzhuiming"] = "当你使用【杀】指定唯一目标后，你可以声明一种颜色且该角色可以弃置任意张牌，然后你展示其一张牌，若此牌的颜色与你声明的颜色相同，此【杀】不计入次数、不能被响应且造成的伤害+1。",

	["zhuiming_dis"] = "【杀】使用来源发动“追命”声明了%srg，你可以弃置任意张牌",
	["$kezhuanzhuiming"] = "%from 声明的颜色为 %arg ！",
	["$kezhuanzhuimingtrigger"] = "%from 的 <font color='yellow'><b>“追命”</s></font> 生效，此【杀】不计入次数、不能被响应且造成的伤害+1！",

	["$kezhuanzhuiming1"] = "以尔等之血，祭我族人！",
	["$kezhuanzhuiming2"] = "去地下忏悔你们的罪行吧！",

	["~kezhuanmachao"] = "西凉众将离心，父仇难报！",


	--张任
	["kezhuanzhangren"] = "张任[转]", 
	["&kezhuanzhangren"] = "张任",
	["#kezhuanzhangren"] = "索命神射",
	["designer:kezhuanzhangren"] = "官方",
	["cv:kezhuanzhangren"] = "官方",
	["illustrator:kezhuanzhangren"] = "鬼画府，极乐",

	["kezhuanfuni"] = "伏匿",
	["kezhuanfuni-distribute"] = "你可以将这些【影】分配给任意名角色",
	[":kezhuanfuni"] = "锁定技，你的攻击范围始终为0；<font color='green'><b>每轮开始时，</s></font>你将游戏外的X张【影】交给任意名角色（X为存活角色数的一半，向上取整）；当一张【影】进入弃牌堆时，你当前回合使用牌无距离限制且不能被响应。",

	["kezhuanchuanxin"] = "穿心",
	["kezhuanchuanxin-ask"] = "穿心：你可以将一张牌当【杀】使用",
	[":kezhuanchuanxin"] = "一名角色的<font color='green'><b>结束阶段，</s></font>你可以将一张牌当【杀】使用；你以此法使用的【杀】对一名角色造成伤害时，伤害值+X（X为其当前回合回复过的体力值）。",
	["$kezhuandestroyEquip"] = "%card 被销毁！",
	["$kezhuanchuanxinda"] = "%from 本回合回复了 %arg 点体力，此伤害值增加等量数值 !",
	
	["$kezhuanfuni1"] = "进入埋伏，倒要看你如何脱身。",
	["$kezhuanfuni2"] = "谅你肋生双翅，也逃不出这天罗地网。",
	["$kezhuanchuanxin1"] = "弩搭穿心箭，葬敌落凤坡。",
	["$kezhuanchuanxin2"] = "麒麟弓一出，定穿心取命。",

	["~kezhuanzhangren"] = "诸将无能，悉数终亡。",

	["$kezhuanfunixiangying"] = "%from 的 <font color='yellow'><b>“伏匿”</s></font> 触发，此牌不能被响应！",


	--张飞
	["kezhuanzhangfei"] = "张飞[转]", 
	["&kezhuanzhangfei"] = "张飞",
	["#kezhuanzhangfei"] = "长坂之威",
	["designer:kezhuanzhangfei"] = "官方",
	["cv:kezhuanzhangfei"] = "官方",
	["illustrator:kezhuanzhangfei"] = "鬼画府",

	["kezhuanbaohe"] = "暴喝",
	[":kezhuanbaohe"] = "一名角色的<font color='green'><b>出牌阶段结束时，</s></font>你可以弃置两张牌视为对攻击范围内包含其的所有其他角色使用一张【杀】；你以此法使用的【杀】造成的伤害+X（X为此牌被响应的次数）。",
	["$kezhuanbaoheda"] = "%from 的 <font color='yellow'><b>“暴喝”</s></font> 触发，此牌伤害 + %arg ！",

	["kezhuanxushi"] = "虚势",
	[":kezhuanxushi"] = "出牌阶段限一次，你可以交给任意名其他角色各一张牌，然后从游戏外获得2X张【影】（X为你给出的牌数）。",


	["kezhuanbaohe-ask"] = "你可以弃置两张牌对 %src 发动“暴喝”",
	["kezhuanxushigive"] = "请选择交给 %src 的牌",

	["$kezhuanbaohe1"] = "哇呀呀呀呀呀！",
	["$kezhuanbaohe2"] = "此声一震，桥断水停！",
	["$kezhuanxushi1"] = "我燕人自有妙计！",
	["$kezhuanxushi2"] = "偃旗息鼓，蓄势待发！",

	["~kezhuanzhangfei"] = "我这脾气，该收敛收敛了。",


	--夏侯荣
	["kezhuanxiahourong"] = "夏侯荣[转]", 
	["&kezhuanxiahourong"] = "夏侯荣",
	["#kezhuanxiahourong"] = "擐甲执兵",
	["designer:kezhuanxiahourong"] = "官方",
	["cv:kezhuanxiahourong"] = "傲雪梅枪",
	["illustrator:kezhuanxiahourong"] = "鬼画府，极乐",

	["kezhuanfenjian"] = "奋剑",

	[":kezhuanfenjian"] = "<font color='green'><b>每回合各限一次，</s></font>你可以令你当前回合受到的伤害+1视为使用一张【决斗】或对一名处于濒死状态的其他角色使用一张【桃】。",
	
	["$kezhuanfenjian1"] = "临险必夷，背水一战！",
	["$kezhuanfenjian2"] = "处变之际，决胜之间！",

	["~kezhuanxiahourong"] = "天下已定，我固当烹！",

	--孙尚香
	["kezhuansunshuangxiang"] = "孙尚香[转]", 
	["&kezhuansunshuangxiang"] = "孙尚香",
	["#kezhuansunshuangxiang"] = "情断吴江",
	["designer:kezhuansunshuangxiang"] = "官方",
	["cv:kezhuansunshuangxiang"] = "官方",
	["illustrator:kezhuansunshuangxiang"] = "鬼画府，极乐",

	["kezhuanguiji"] = "闺忌",
	["kezhuanguijiagain"] = "闺忌：与其交换手牌",
	--[":kezhuanguiji"] = "出牌阶段，你可以与一名手牌数小于你的男性角色交换手牌，若如此做，“闺忌”失效直到其死亡时，或其下个<font color='green'><b>出牌阶段结束时，</s></font>你可以与其交换手牌。",
	[":kezhuanguiji"] = "出牌阶段限一次，你可以与一名手牌数小于你的男性角色交换手牌，然后“闺忌”失效直到满足下列一项:\
	1.该角色下个<font color='green'><b>出牌阶段结束时</s></font>，且你可以与其交换手牌；\
	2.该角色死亡时。",

	["kezhuanjiaohaoex"] = "骄豪放牌",
	[":kezhuanjiaohaoex"] = "出牌阶段限一次，你可以将手牌中的一张装备牌置于一名拥有“骄豪”的角色对应空置的装备栏中。",
	["kezhuanjiaohao"] = "骄豪",
	[":kezhuanjiaohao"] = "其他角色的出牌阶段限一次，其可以将手牌中的一张装备牌置于你对应空置的装备栏中；<font color='green'><b>准备阶段，</s></font>你从游戏外获得X张【影】（X为你空置的装备栏数的一半，向上取整）。",

	["$kezhuanguiji1"] = "鸾凤和鸣，情投意合。",
	["$kezhuanguiji2"] = "双剑同鸣，双心灵犀。",
	["$kezhuanjiaohao1"] = "边月随弓影，胡霜拂剑花！",
	["$kezhuanjiaohao2"] = "轻叶心间过，刀剑光影掠！",
	["$kezhuanjiaohao3"] = "这些都交给我吧！",
	["$kezhuanjiaohao4"] = "那小女子就却之不恭喽！",

	["~kezhuansunshuangxiang"] = "何处吴歌起，夜望不知乡。",


	--黄忠
	["kezhuanhuangzhong"] = "黄忠[转]", 
	["&kezhuanhuangzhong"] = "黄忠",
	["#kezhuanhuangzhong"] = "定军之英",
	["designer:kezhuanhuangzhong"] = "官方",
	["cv:kezhuanhuangzhong"] = "官方",
	["illustrator:kezhuanhuangzhong"] = "鬼画府",

	["kezhuancuifeng"] = "摧锋",
	["kezhuancuifeng-ask"] = "请选择此【%src】的目标 -> 点击确定",
	["kezhuancuifengchongzhi"] = "摧锋重置",
	[":kezhuancuifeng"] = "限定技，出牌阶段，你可以视为使用一张指定唯一目标的伤害类牌（不为延时类锦囊牌，无距离限制），若此牌没有造成伤害或造成的总伤害值大于1，本<font color='green'><b>回合结束时，</s></font>“摧锋”视为未发动过。",


	["kezhuandengnan"] = "登难",
	[":kezhuandengnan"] = "限定技，出牌阶段，你可以视为使用一张非伤害类普通锦囊牌，若此牌的目标均于本回合受到过伤害，本<font color='green'><b>回合结束时，</s></font>“登难”视为未发动过。",
	["kezhuandengnanover"] = "登难目标达成",
	["kezhuandengnantar"] = "登难目标",
	["kezhuandengnanda"] = "已受到伤害",
	["kezhuandengnan-ask"] = "请选择此【%src】的目标 -> 点击确定",

	["$kezhuandengnan1"] = "一箭从戎起长沙，射得益州做汉家！",
	["$kezhuandengnan2"] = "将拜五虎从风雨，功夸定军造乾坤！",
	["$kezhuancuifeng1"] = "龙骨成镞，矢破苍穹。",
	["$kezhuancuifeng2"] = "凤翎为羽，箭没坚城。",

	["~kezhuanhuangzhong"] = "末将，有负主公重托。",


	--娄圭
	["kezhuanlougui"] = "娄圭[转]", 
	["&kezhuanlougui"] = "娄圭",
	["#kezhuanlougui"] = "梦梅居士",
	["designer:kezhuanlougui"] = "官方",
	["cv:kezhuanlougui"] = "三国演义",
	["illustrator:kezhuanlougui"] = "鬼画府",

	["kezhuanshacheng"] = "沙城",
	
	["kezhuanshacheng-ask"] = "你可以选择“沙城”摸牌的目标角色",
	[":kezhuanshacheng"] = "<font color='green'><b>游戏开始时，</s></font>你将牌堆顶的两张牌置于武将牌上，称为“沙城”；当一名角色使用的【杀】结算完毕后，你可以将一张“沙城”置入弃牌堆并令一名目标角色摸X张牌（X为其当前回合失去的牌数且至多为5）。",
	["kezhuanshacheng:kezhuanshacheng-ask"] = "你可以发动“沙城”令一名目标角色摸牌",


	["kezhuanninghan"] = "凝寒",
	[":kezhuanninghan"] = "锁定技，所有角色手牌中的♣【杀】均视为冰【杀】；当一名角色受到冰冻伤害后，你可以将造成此伤害的牌置于武将牌上，称为“沙城”。",
	["kezhuanninghan:kezhuanninghan-ask"] = "你可以发动“凝寒”将 %src 置于武将牌上",
	["kezhuanninghanbuff"] = "凝寒杀",
	[":kezhuanninghanbuff"] = "锁定技，你手牌中的♣【杀】均视为冰【杀】。",

	["kezhuanshachengcandraw"] = "沙城可摸牌",

	["$kezhuanshacheng1"] = "天色已晚，丞相为何不筑城建营呢？",
	["$kezhuanshacheng2"] = "晚上极冷，边筑土边泼水，马上冻结，随筑随冻，不就成了？",
	["$kezhuanninghan1"] = "哈哈哈哈哈，丞相熟知兵法，难道不知因时而动？",
	["$kezhuanninghan2"] = "丞相，我只是希望您能早日统一天下，让百姓脱离战乱之苦。",

	["~kezhuanlougui"] = "啊，请丞相好自为之。",




	--韩遂
	["kezhuanhansui"] = "韩遂[转]", 
	["&kezhuanhansui"] = "韩遂",
	["#kezhuanhansui"] = "雄踞北疆",
	["designer:kezhuanhansui"] = "官方",
	["cv:kezhuanhansui"] = "官方",
	["illustrator:kezhuanhansui"] = "盲特",

	["kezhuanniluan"] = "逆乱",
	["kezhuanniluan-ask"] = "你可以发动“逆乱”",
	[":kezhuanniluan"] = "<font color='green'><b>准备阶段，</s></font>你可以令一名对你造成过伤害的角色摸两张牌，或弃置一张牌对一名未对你造成过伤害的角色造成1点伤害。",
	["$kezhuanniluanlog"] = "%from 发动了 <font color='yellow'><b>“逆乱”</s></font> ",

	["kezhuanhuchou"] = "互雠",
	[":kezhuanhuchou"] = "锁定技，你对上一名对你使用伤害类牌的角色造成的伤害+1。",

	["kezhuanjiemeng"] = "皆盟",
	[":kezhuanjiemeng"] = "主公技，锁定技，群势力角色与其他角色的距离-X（X为群势力角色数）。",

	["$kezhuanniluan1"] = "天下动乱，我怎能坐视不管？",
	["$kezhuanniluan2"] = "骁雄武力，岂可甘为他将？",
	["$kezhuanhuchou1"] = "众十余万，天下扰动。",
	["$kezhuanhuchou2"] = "诛杀宦官，吾亦出力！",

	["~kezhuanhansui"] = "称雄三十载，一败化为尘。",


	--张楚
	["kezhuanzhangchu"] = "张楚[转]", 
	["&kezhuanzhangchu"] = "张楚",
	["#kezhuanzhangchu"] = "大贤后裔",
	["designer:kezhuanzhangchu"] = "官方",
	["cv:kezhuanzhangchu"] = "官方",
	["illustrator:kezhuanzhangchu"] = "花第",

	["kezhuanhuozhong"] = "惑众",
	["kezhuanhuozhongex"] = "惑众放牌",
	[":kezhuanhuozhong"] = "每名角色的出牌阶段限一次，其可以将一张黑色非锦囊牌当【兵粮寸断】置于其判定区内，然后令一名拥有“惑众”的角色摸两张牌。",
	[":kezhuanhuozhongex"] = "出牌阶段限一次，你可以将一张黑色非锦囊牌当【兵粮寸断】置于你的判定区内，然后令一名拥有“惑众”的角色摸两张牌。",

	["kezhuanrihui"] = "日慧",
	[":kezhuanrihui"] = "当你使用【杀】对目标角色造成伤害后，你可以令判定区有牌的其他角色各摸一张牌；你每回合对判定区没有牌的角色使用的第一张【杀】无次数限制。",

	["kezhuanrihui:kezhuanrihui"] = "你可以发动“日慧”令判定区有牌的角色各摸一张牌",

	["$kezhuanhuozhong1"] = "天地裹黄巾者无数，如麦粟绽于秋雨。",
	["$kezhuanhuozhong2"] = "天地之不仁者，吾可登长辇而伐天地。",
	["$kezhuanrihui1"] = "今连方七十二，宁为战魂，勿做刍狗。",
	["$kezhuanrihui2"] = "吾父黄泉未远，定可见黄天再现人间。",

	["~kezhuanzhangchu"] = "大贤良师之女，不畏一死。",

	--夏侯恩
	["kezhuanxiahouen"] = "夏侯恩[转]", 
	["&kezhuanxiahouen"] = "夏侯恩",
	["#kezhuanxiahouen"] = "背剑之将",
	["designer:kezhuanxiahouen"] = "官方",
	["cv:kezhuanxiahouen"] = "官方",
	["illustrator:kezhuanxiahouen"] = "蚂蚁君",

	["kezhuanhujian"] = "护剑",
	[":kezhuanhujian"] = "<font color='green'><b>游戏开始时，</s></font>你从游戏外获得一张【赤血青锋】；一个<font color='green'><b>回合结束时，</s></font>此回合最后一名使用或打出牌的角色可以获得弃牌堆中的【赤血青锋】。",
	["kezhuanhujian:kezhuanhujian-ask"] = "护剑：你可以获得弃牌堆中的【赤血青锋】",

	["kezhuanshili"] = "恃力",
	[":kezhuanshili"] = "出牌阶段限一次，你可以将手牌中的一张装备牌当【决斗】使用。",

	["$kezhuanhujian1"] = "得此宝剑，如虎添翼！",
	["$kezhuanhujian2"] = "丞相之宝，汝岂配用之？啊哈！",
	["$kezhuanshili1"] = "小小匹夫，可否闻长坂剑神之名啊？",
	["$kezhuanshili2"] = "此剑吹毛得过，削铁如泥！",

	["~kezhuanxiahouen"] = "长坂剑神，也陨落了。",


	--庞统
	["kezhuanpangtong"] = "庞统[转]", 
	["&kezhuanpangtong"] = "庞统",
	["#kezhuanpangtong"] = "荆楚之高俊",
	["designer:kezhuanpangtong"] = "官方",
	["cv:kezhuanpangtong"] = "官方",
	["illustrator:kezhuanpangtong"] = "鬼画府，极乐",

	["kezhuanmanjuan"] = "漫卷",
	[":kezhuanmanjuan"] = "每回合每种点数限一次，若你没有手牌，你可以使用或打出本回合置入弃牌堆的牌。",

	["kezhuanyangming"] = "养名",
	[":kezhuanyangming"] = "出牌阶段限一次，你可以与一名角色拼点：若其赢，其摸X张牌（X为其本阶段拼点没赢的次数）且你回复1点体力，否则你可以对其重复此流程。",
	
	["kezhuanyangming:kezhuanyangming-jixu"] = "你可以发动“养名”继续与 %src 拼点",
	["kezhuanyangminglose"] = "拼点没赢",

	["kezhuanmanjuan0"] = "你可以使用其中一张牌",
	["kezhuanmanjuan2"] = "请选择此牌的目标 -> 点击确定",
	["kezhuanmanjuan1"] = "你可以使用此牌",

	["$kezhuanmanjuan1"] = "吾非百里才，必有千里之行。",
	["$kezhuanmanjuan2"] = "展吾骥足，施吾羽翅！",
	["$kezhuanyangming1"] = "表虽言过其实，实则引人向善。",
	["$kezhuanyangming2"] = "吾与卿之才干，孰高孰低？",

	["~kezhuanpangtong"] = "雏凤未飞已先陨。",


	--范疆＆张达
	["kezhuanfanjiangzhangda"] = "范疆＆张达[转]", 
	["&kezhuanfanjiangzhangda"] = "范疆＆张达",
	["#kezhuanfanjiangzhangda"] = "你死我亡",
	["designer:kezhuanfanjiangzhangda"] = "官方",
	["cv:kezhuanfanjiangzhangda"] = "官方",
	["illustrator:kezhuanfanjiangzhangda"] = "游漫美绘",

	["kezhuanfushan"] = "负山",
	[":kezhuanfushan"] = "<font color='green'><b>出牌阶段开始时，</s></font>所有其他角色可以依次选择是否交给你一张牌并令你此阶段可以多使用一张【杀】；<font color='green'><b>出牌阶段结束时，</s></font>若你使用【杀】的剩余次数不为0且此阶段以此法交给你牌的角色均存活，你失去2点体力，否则你将手牌摸至体力上限。",

	["kezhuanfushangive"] = "负山：你可以交给 %src 一张牌",

	["$kezhuanfushan1"] = "鞭鞭入肉，似钢钉入骨，此仇如何消得？",
	["$kezhuanfushan2"] = "斥我如奴，鞭我如畜，如何叫我以德报怨？",

	["~kezhuanfanjiangzhangda"] = "什么！刘备伐吴了？",


	--蔡瑁＆张允
	["kezhuancaimaozhangyun"] = "蔡瑁＆张允[转]", 
	["&kezhuancaimaozhangyun"] = "蔡瑁＆张允",
	["#kezhuancaimaozhangyun"] = "乘雷潜狡",
	["designer:kezhuancaimaozhangyun"] = "官方",
	["cv:kezhuancaimaozhangyun"] = "官方",
	["illustrator:kezhuancaimaozhangyun"] = "君桓文化",
	["~kezhuancaimaozhangyun"] = "丞相，冤枉，冤枉啊！",

	--黄承彦
	["kezhuanhuangchengyan"] = "黄承彦[转]", 
	["&kezhuanhuangchengyan"] = "黄承彦",
	["#kezhuanhuangchengyan"] = "沔阳雅士",
	["designer:kezhuanhuangchengyan"] = "官方",
	["cv:kezhuanhuangchengyan"] = "官方",
	["illustrator:kezhuanhuangchengyan"] = "凡果",
	["~kezhuanhuangchengyan"] = "卧龙出山天伦逝，悔教吾婿离南阳。",

		--蒋干
	["kezhuanjianggan"] = "蒋干[转]", 
	["&kezhuanjianggan"] = "蒋干",
	["#kezhuanjianggan"] = "锋镝悬信",
	["designer:kezhuanjianggan"] = "官方",
	["cv:kezhuanjianggan"] = "官方",
	["illustrator:kezhuanjianggan"] = "biou09",
	["~kezhuanjianggan"] = "丞相，再给我一次机会啊！",

		--阚泽
	["kezhuankanze"] = "阚泽[转]", 
	["&kezhuankanze"] = "阚泽",
	["#kezhuankanze"] = "慧眼的博士",
	["designer:kezhuankanze"] = "官方",
	["cv:kezhuankanze"] = "官方",
	["illustrator:kezhuankanze"] = "游漫美绘",
	["~kezhuankanze"] = "谁又能来宽释我呢？",

}
return {extension}

