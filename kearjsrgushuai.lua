--==《新武将》==--
extension = sgs.Package("kearjsrgushuai", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

function KeToData(self)
	local data = sgs.QVariant()
	if type(self)=="string"
	or type(self)=="boolean"
	or type(self)=="number"
	then data = sgs.QVariant(self)
	elseif self~=nil then data:setValue(self) end
	return data
end
--buff集中
keshuaislashmore = sgs.CreateTargetModSkill{
	name = "keshuaislashmore",
	pattern = ".",
	residue_func = function(self, from, card, to)
		local n = 0
		if to and (to:getMark("&keshuaizhuni-Clear") > 0) and from:hasSkill("keshuaizhuni") then
			n = n + 1000
		end
		return n
	end,
	extra_target_func = function(self, from, card)
		local n = 0
		if card:isKindOf("FireAttack") 
		and card:getSkillName() == "keshuaiguanshi"
		and from:hasSkill("keshuaiguanshi") then
			n = n + 1000
		end
		return n
	end,
	distance_limit_func = function(self, from, card, to)
		local n = 0
		if to and (to:getMark("&keshuaizhuni-Clear") > 0) and from:hasSkill("keshuaizhuni") then
			n = n + 1000
		end
		if card:isKindOf("Slash") and card:getSkillName() == "keshuaiyansha" then
			n = n + 1000
		end
		return n
	end
}
if not sgs.Sanguosha:getSkill("keshuaislashmore") then skills:append(keshuaislashmore) end

keshuaiyuanshao = sgs.General(extension, "keshuaiyuanshao$", "qun", 4)

keshuaizhimeng = sgs.CreateTriggerSkill{
	name = "keshuaizhimeng",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and (player:getPhase() == sgs.Player_Start) then
			if room:askForSkillInvoke(player, self:objectName(), data) then
				local card_ids = room:showDrawPile(player,player:aliveCount(),self:objectName())
				local zss = {}
				--选牌，记录
				for _, q in sgs.qlist(room:getAllPlayers()) do   
					if q:isKongcheng() then continue end
					zss[q:objectName()] = room:askForExchange(q, "keshuaizhimeng", 1, 1, false, "keshuaizhimengask")
				end
				local allshows = sgs.CardList()
				--展示，记录大家展示的牌
				for _, q in sgs.qlist(room:getAllPlayers()) do   
					local dc = zss[q:objectName()]
					if dc then
						allshows:append(dc)
						room:showCard(q,dc:getEffectiveId())
						zss[q:objectName()] = dc:getSuit()
					end
				end
				room:getThread():delay(1200)
				--结算：若展示的牌里只有一张跟自己的符合，那么自己刚才展示的就是唯一的
				for _, q in sgs.qlist(room:getAllPlayers()) do   
					local same = 0
					local thesuit = -1
					for _,c in sgs.qlist(allshows) do
						local suitnum = c:getSuit()
						if (suitnum == zss[q:objectName()]) then
							same = same + 1
							thesuit = suitnum
							if same>1 then break end
						end
					end
					if (same == 1) then
						local dummy = sgs.Sanguosha:cloneCard("slash")
						for _,idd in sgs.qlist(card_ids) do
							if (sgs.Sanguosha:getCard(idd):getSuit() == thesuit) then
								--获得的牌从一开始的里面移除
								dummy:addSubcard(idd)
							end
						end
						if dummy:subcardsLength() > 0 then
							local log = sgs.LogMessage()
							log.type = "$keshuaizhimenglog"
							log.from = q
							room:sendLog(log)
							q:obtainCard(dummy)
							for _,idd in sgs.qlist(dummy:getSubcards()) do
								card_ids:removeOne(idd)
							end
						end
						dummy:deleteLater()
					end
				end
				--弃置其他亮出的牌
				local dummy = sgs.Sanguosha:cloneCard("slash")
				dummy:addSubcards(card_ids)
				room:throwCard(dummy,self:objectName(),nil)
				dummy:deleteLater()		
			end
		end
	end,
}
keshuaiyuanshao:addSkill(keshuaizhimeng)

keshuaitianyu = sgs.CreateTriggerSkill{
    name = "keshuaitianyu",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseChanging,sgs.CardsMoveOneTime},
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from then
				local tag = room:getTag("keshuaitianyutag"):toIntList()
				for i,card_id in sgs.qlist(move.card_ids) do
					if move.from_places:at(i)==sgs.Player_PlaceEquip
					or move.from_places:at(i)==sgs.Player_PlaceHand
					then tag:append(card_id) end
				end
				room:setTag("keshuaitianyutag", ToData(tag))
			end
			if (move.to_place == sgs.Player_DiscardPile) then
				local tag = room:getTag("keshuaitianyutag"):toIntList()
				for _,card_id in sgs.qlist(move.card_ids) do
					if tag:contains(card_id) then continue end
					local c = sgs.Sanguosha:getCard(card_id)
					if (c:isKindOf("EquipCard") or c:isDamageCard())
					and player:hasSkill(self:objectName()) then
						if player:askForSkillInvoke(self,KeToData("keshuaitianyuask:"..c:objectName())) then
							room:obtainCard(player,card_id)
						end
					end
				end
			end
		end
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if change.from == sgs.Player_NotActive then
				room:removeTag("keshuaitianyutag")
			end
		end
	end
}
keshuaiyuanshao:addSkill(keshuaitianyu)

keshuaizhuniCard = sgs.CreateSkillCard{
	name = "keshuaizhuniCard" ,
	target_fixed = true,
	will_throw = false,
	--[[filter = function(self, targets, to_select, from)
		return (#targets == 0) and (to_select:objectName() ~= from:objectName()) 
	end,]]
	on_use = function(self, room, player, targets)
		local record = {}
		local Lord = nil
		for _,p in sgs.qlist(room:getAllPlayers()) do
			local one = room:askForPlayerChosen(p, room:getOtherPlayers(player), self:getSkillName(), "keshuaizhuni_ask", false)
			if one then
				--先记录，等会展示
				--耦一个主公技
				if p:hasLordSkill("keshuaihezhi") then
					room:sendCompulsoryTriggerLog(p,"keshuaihezhi")
					Lord = p
				end
				record[p:objectName()] = one
			end
		end
		--更改其他角色的选择
		for _,p in sgs.qlist(room:getAllPlayers()) do
			if (p:getKingdom() == "qun") and Lord and Lord~=p then 
				record[p:objectName()] = record[Lord:objectName()]
			end
		end
		--同时开始选择
		for _,p in sgs.qlist(room:getAllPlayers()) do
			local log = sgs.LogMessage()
			log.type = "$keshuaizhunilog"
			log.from = p
			log.to:append(record[p:objectName()])
			room:sendLog(log)
			room:doAnimate(1,p:objectName(),record[p:objectName()]:objectName())
			room:addPlayerMark(record[p:objectName()],"&keshuaizhunicount")
			room:getThread():delay(400)
		end
		room:getThread():delay(600)
		local themost = player
		for _,p in sgs.qlist(room:getAllPlayers()) do
			if p:getMark("&keshuaizhunicount") > themost:getMark("&keshuaizhunicount") then
				themost = p
			end
		end
		--检查唯一
		local weiyi = 1
		for _,oth in sgs.qlist(room:getOtherPlayers(themost)) do
			if (oth:getMark("&keshuaizhunicount") >= themost:getMark("&keshuaizhunicount")) then
				weiyi = 0
				break
			end
		end
		if (weiyi == 1) then
			local log = sgs.LogMessage()
			log.type = "$keshuaizhunitarget"
			log.from = themost
			room:sendLog(log)
			room:setPlayerMark(themost,"&keshuaizhuni-Clear",1)
		end
		for _,p in sgs.qlist(room:getAllPlayers()) do
			room:setPlayerMark(p,"&keshuaizhunicount",0)
		end
	end
}

keshuaizhuni = sgs.CreateZeroCardViewAsSkill{
	name = "keshuaizhuni",
	enabled_at_play = function(self, player)
		return not player:hasUsed("#keshuaizhuniCard") 
	end ,
	view_as = function()
		return keshuaizhuniCard:clone()
	end
}
keshuaiyuanshao:addSkill(keshuaizhuni)

keshuaihezhi = sgs.CreateTriggerSkill{
	name = "keshuaihezhi$",
	frequency = sgs.Skill_Compulsory,
	events = {},
	on_trigger = function(self, event, player, data)
	
	end ,
	can_trigger = function(self, target)
		return false
	end
}
keshuaiyuanshao:addSkill(keshuaihezhi)


sgs.LoadTranslationTable{
    ["kearjsrgushuai"] = "江山如故·衰",

	["keshuaiyuanshao"] = "袁绍[衰]", 
	["&keshuaiyuanshao"] = "袁绍",
	["#keshuaiyuanshao"] = "号令天下",
	["designer:keshuaiyuanshao"] = "官方",
	["cv:keshuaiyuanshao"] = "官方",
	["illustrator:keshuaiyuanshao"] = "鬼画府",
	["information:keshuaiyuanshao"] = "ᅟᅠ<i>《旧陈书卷一圣武帝纪》\
	ᅟᅠ太祖圣武皇帝。汝南汝阳人也，姓袁，讳绍，字本初。太祖于黎阳梦有一神授一宝刀，及觉，果在卧所，铭曰思召。解之曰：思召，绍字也。\
	ᅟᅠ……灵帝崩，少帝继位。卓议欲废立，太祖拒之，卓案剑吆曰：“竖子敢然！天下之事，岂不在我？我欲为之，谁敢不从！”绍勃然曰：“天下健者，岂惟董乎！”横剑径出。世入方知太祖贤名非以权势取之，实乃英雄气也。\
	ᅟᅠ初平元年，太祖于勃海起兵，其从弟后将军术等十余位诸侯同时俱起，兴兵讨董。是时，豪杰既多附招，州郡蜂起，莫不以袁氏为名。……太祖既得冀州，尝出猎白登山，见一白鹿口含宝剑而来，获之，剑名中兴。或曰：汉失其鹿，陈逐而获之。\
	ᅟᅠ建安五年，太祖与曹操战于官渡，曹操欲夜袭乌巢，恰有流星如火。光长十余丈照于曹营，昼有云如坏山，当营而陨，不及地尺而散，吏士皆以为不详，太祖并兵俱攻大破之，操自军破后，头风病发，六年夏五月死。</i>",

	["keshuaizhimeng"] = "执盟",
	["keshuaizhimengask"] = "执盟：请选择一张手牌展示",
	[":keshuaizhimeng"] = "准备阶段，你可以亮出牌堆顶等同于存活角色数量的牌，所有角色同时展示一张手牌，然后展示花色唯一的角色获得亮出的牌中该花色的所有牌。",
	["$keshuaizhimengshowlog"] = "%from 发动<font color='yellow'><b>“执盟”</b></font>展示了 %card",
	["$keshuaizhimenglog"] = "%from 因<font color='yellow'><b>“执盟”</b></font>展示的牌花色唯一，将获得亮出的牌中该花色的所有牌",

	["keshuaitianyu"] = "天予",
	["keshuaitianyu:keshuaitianyuask"] = "你可以发动“天予”获得【 %src 】",
	[":keshuaitianyu"] = "当一张伤害类牌或装备牌进入弃牌堆时，若此牌当前回合内没有离开过任意一名角色的手牌区或装备区，你可以获得之。",

	["keshuaizhuni"] = "诛逆",
	["$keshuaizhunilog"] = "%from 因<font color='yellow'><b>“诛逆”</b></font> 选择了 %to",
	["$keshuaizhunitarget"] = "%from 被选择次数最多，成为本次<font color='yellow'><b>“诛逆”</b></font> 的目标",
	["keshuaizhunicount"] = "诛逆次数",
	[":keshuaizhuni"] = "出牌阶段限一次，你可以令所有角色同时选择一名除你以外的角色，然后你本回合对被以此法选择次数唯一最多的角色使用牌无距离和次数限制。",
	["keshuaizhuni_ask"] = "诛逆：请选择一名角色",

	["keshuaihezhi"] = "合志",
	[":keshuaihezhi"] = "主公技，锁定技，其他群势力角色因“诛逆”选择的角色改为与你相同。",

	["$keshuaizhimeng1"] = "",
	["$keshuaizhimeng2"] = "",


	["~keshuaiyuanshao"] = "",
}







keshuaizhangjiao = sgs.General(extension, "keshuaizhangjiao", "qun", 4)

keshuaixiangru = sgs.CreateTriggerSkill{
	name = "keshuaixiangru",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageInflicted) then
			local damage = data:toDamage()
			if damage.from and damage.damage>=damage.to:getHp()+damage.to:getHujia() then
				--两种情况都可以触发，因为其他人也可能有本技能，所以逻辑不用else
				if damage.to:hasSkill(self:objectName()) then
					for _, oth in sgs.qlist(room:getOtherPlayers(damage.to)) do
						if damage.from~=oth and oth:isWounded() and oth:getCardCount()>1 then
							oth:setTag("keshuaixiangruTo",ToData(damage.to))
							local card = room:askForExchange(oth, self:objectName(), 2, 2, true, "keshuaixiangruchoose:"..damage.from:objectName()..":"..damage.to:objectName(),true)
							if card then
								oth:skillInvoked(self,-1,damage.to)
								room:obtainCard(damage.from, card, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, damage.from:objectName(), oth:objectName(), self:objectName(), ""), false)
								return true
							end
						end
					end
				elseif damage.to:isWounded() then
				    for _, zj in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
						if zj:getCardCount()<2 or damage.from==zj then continue end
						zj:setTag("keshuaixiangruTo",ToData(damage.to))
						local card = room:askForExchange(zj, self:objectName(), 2, 2, true, "keshuaixiangruchoose:"..damage.from:objectName()..":"..damage.to:objectName(),true)
						if card then
							zj:skillInvoked(self,-1)
							room:obtainCard(damage.from, card, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, damage.from:objectName(), zj:objectName(), self:objectName(), ""), false)
							return true
						end
					end
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end
}
keshuaizhangjiao:addSkill(keshuaixiangru)

keshuaiwudao = sgs.CreateTriggerSkill{
	name = "keshuaiwudao",
	frequency = sgs.Skill_Wake,
	waked_skills = "keshuaijinglei",
	events = {sgs.EnterDying},
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EnterDying) then
			for _, zj in sgs.qlist(room:getAllPlayers())do
				if zj:getMark(self:objectName())<1 and zj:hasSkill(self)
				and (zj:isKongcheng() or zj:canWake(self:objectName())) then
					room:sendCompulsoryTriggerLog(zj,self)
					room:doSuperLightbox(zj, "keshuaiwudao")
					room:changeMaxHpForAwakenSkill(zj,1,self:objectName())
					room:recover(zj, sgs.RecoverStruct(self:objectName(),zj))
					room:setPlayerMark(zj, self:objectName(), 1)
					room:acquireSkill(zj, "keshuaijinglei")
				end
			end
		end
	end,
}
keshuaizhangjiao:addSkill(keshuaiwudao)

keshuaijingleiCard = sgs.CreateSkillCard{
	name = "keshuaijingleiCard",
	target_fixed = false,
	will_throw = false,
	skill_name = "_keshuaijinglei",
	filter = function(self, targets, to_select, player)
		local he = 0
		for _, p in ipairs(targets) do
			he = he + p:getHandcardNum()
		end
		return (he + to_select:getHandcardNum()) < player:getMark("keshuaijinglei")
	end,
	on_use = function(self, room, player, targets)
		local jltarget
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if p:getMark("&keshuaijinglei") > 0 then
				jltarget = p
				break
			end
		end
		for _, p in ipairs(targets) do
			room:getThread():delay(666)
			room:damage(sgs.DamageStruct("keshuaijinglei", p, jltarget, 1,sgs.DamageStruct_Thunder))
		end
	end
}

keshuaijingleiVS = sgs.CreateViewAsSkill{
	name = "keshuaijinglei",
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return false
	end ,
	view_as = function(self, cards)
		return keshuaijingleiCard:clone()
	end ,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@keshuaijinglei"
	end,
	enabled_at_play = function(self, player)
		return false
	end
}
keshuaijinglei = sgs.CreateTriggerSkill{
	name = "keshuaijinglei",
	events = {sgs.EventPhaseStart},
	view_as_skill = keshuaijingleiVS,
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) 
		and (player:getPhase() == sgs.Player_Start) then
			local eny = room:askForPlayerChosen(player, room:getAllPlayers(), self:objectName(), "keshuaijinglei-ask",true,true)
			if eny then
				room:setPlayerMark(player,"keshuaijinglei",eny:getHandcardNum())
				room:setPlayerMark(eny,"&keshuaijinglei",1)
			    room:askForUseCard(player, "@@keshuaijinglei", "keshuaijingleiask")
				room:setPlayerMark(eny,"&keshuaijinglei",0)
			end
		end
	end,
}
if not sgs.Sanguosha:getSkill("keshuaijinglei") then skills:append(keshuaijinglei) end


sgs.LoadTranslationTable{

	["keshuaizhangjiao"] = "张角[衰]", 
	["&keshuaizhangjiao"] = "张角",
	["#keshuaizhangjiao"] = "万蛾赴火",
	["designer:keshuaizhangjiao"] = "官方",
	["cv:keshuaizhangjiao"] = "官方",
	["illustrator:keshuaizhangjiao"] = "鬼画府",

	["keshuaixiangru"] = "相濡",
	["keshuaixiangruchoose"] = "你可以发动“相濡”交给 %src 两张牌，防止 %dest 受到的致命伤害",
	[":keshuaixiangru"] = "你/已受伤的其他角色可以交给伤害来源两张牌防止已受伤的其他角色/你受到的致命伤害。",

	["keshuaiwudao"] = "悟道",
	[":keshuaiwudao"] = "觉醒技，当一名角色进入濒死状态时，若你没有手牌，你加1点体力上限并回复1点体力，然后获得“惊雷”。",

	["keshuaijinglei"] = "惊雷",
	["keshuaijingleiask"] = "你可以选择任意名手牌数之和小于其的角色",
	["keshuaijinglei-ask"] = "你可以选择发动“惊雷”的角色",
	["keshuaijinglei"] = "惊雷",
	[":keshuaijinglei"] = "准备阶段，你可以选择一名角色，然后令任意名手牌数之和小于其的角色各对其造成1点雷电伤害。",

	["$keshuaixiangru1"] = "",
	["$keshuaixiangru2"] = "",


	["~keshuaizhangjiao"] = "",
}

keshuailiubiao = sgs.General(extension, "keshuailiubiao", "qun", 3)

keshuaiyanshavs = sgs.CreateViewAsSkill{
	name = "keshuaiyansha",
	n = 1,
	view_filter = function(self, selected, to_select)
		local slash = dummyCard()
		slash:setSkillName("_keshuaiyansha")
		slash:addSubcard(to_select)
		return to_select:isKindOf("EquipCard")
		and not sgs.Self:isLocked(slash)
	end,
	view_as = function(self, cards)
		if #cards ~= 1 then return end
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("_keshuaiyansha")
		slash:addSubcard(cards[1])
		return slash
	end,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern:startsWith("@@keshuaiyansha")
	end
}
--if not sgs.Sanguosha:getSkill("keshuaiyanshavs") then skills:append(keshuaiyanshavs) end


keshuaiyansha = sgs.CreateTriggerSkill{
	name = "keshuaiyansha",
	view_as_skill = keshuaiyanshavs,
	waked_skills = "#keshuaiyanshaslashex",
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and (player:getPhase() == sgs.Player_Start) then
			local tos = sgs.SPlayerList()
			local wgfd = sgs.Sanguosha:cloneCard("amazing_grace")
			wgfd:setSkillName("_keshuaiyansha")
			for _, oth in sgs.qlist(room:getAllPlayers()) do
				if player:canUse(wgfd,oth) then
					tos:append(oth)
				end
			end
			local wgs = room:askForPlayersChosen(player, tos, self:objectName(), 0, 99, "keshuaiyansha-ask", true, true)
			if wgs:length() > 0 then
				for _,oth in sgs.qlist(wgs) do
					room:setPlayerMark(oth,"&keshuaiyansha",1)
				end
				room:useCard(sgs.CardUseStruct(wgfd,player,wgs), true)
				for _,q in sgs.qlist(room:getAllPlayers()) do
					if wgs:contains(q) then continue end
				    room:askForUseCard(q, "@@keshuaiyansha", "keshuaiyanshaslash-ask")
				end
				for _,p in sgs.qlist(wgs) do
					room:setPlayerMark(p,"&keshuaiyansha",0)
				end
			end
			wgfd:deleteLater()
		end
	end,
}
keshuailiubiao:addSkill(keshuaiyansha)
keshuaiyanshaslashex = sgs.CreateProhibitSkill{
	name = "#keshuaiyanshaslashex",
	is_prohibited = function(self, from, to, card)
		return card:getSkillName() == "keshuaiyansha" and card:isKindOf("Slash")
		and to and to:getMark("&keshuaiyansha")<1
	end
}
keshuailiubiao:addSkill(keshuaiyanshaslashex)

keshuaiqingping = sgs.CreateTriggerSkill{
	name = "keshuaiqingping",
	frequency = sgs.Skill_Frequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and (player:getPhase() == sgs.Player_Finish) then
			local num = 0
			local log = sgs.LogMessage()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if player:inMyAttackRange(p) then
					if (p:getHandcardNum() > 0)
					and (p:getHandcardNum() <= player:getHandcardNum()) then
						num = num + 1
						log.to:append(p)
					else
						num = 0
						break
					end
				end
			end
			if num > 0 and player:askForSkillInvoke(self) then
				room:broadcastSkillInvoke(self:objectName())
				log.type = "$keshuaiqingpinglog"
				log.from = player
				room:sendLog(log)
			    player:drawCards(num,self:objectName())
			end
		end
	end,
}
keshuailiubiao:addSkill(keshuaiqingping)



sgs.LoadTranslationTable{

	["keshuailiubiao"] = "刘表[衰]", 
	["&keshuailiubiao"] = "刘表",
	["#keshuailiubiao"] = "单骑入荆",
	["designer:keshuailiubiao"] = "官方",
	["cv:keshuailiubiao"] = "官方",
	["illustrator:keshuailiubiao"] = "鬼画府",

	["keshuaiyansha"] = "宴杀",
	["keshuaiyanshaslash-ask"] = "你可以将一张装备牌当无距离限制的【杀】对一名“宴杀”角色使用",
	["keshuaiyansha-ask"] = "你可以选择发动“宴杀”使用【五谷丰登】的角色",
	[":keshuaiyansha"] = "准备阶段，你可以视为对任意名角色使用一张【五谷丰登】，此牌结算后，不是此牌目标的角色依次选择是否将一张装备牌当无距离限制的【杀】对其中一名目标角色使用。",

	["keshuaiqingping"] = "清平",
	["$keshuaiqingpinglog"] = "%from 满足<font color='yellow'><b>“清平”</b></font> 条件的角色有：%to",
	[":keshuaiqingping"] = "结束阶段，若你的攻击范围内的所有角色手牌数均大于0且不大于你，你可以摸等同于这些角色数量的牌。",

	["$keshuaiyansha1"] = "任行仁义之道，何愁人心不归？",
	["$keshuaiyansha2"] = "稳据江汉，坐观时变。",

	["$keshuaiqingping1"] = "普天之下，莫非汉土。",
	["$keshuaiqingping2"] = "汉室宗亲，同出一门，何须多礼？",

	["~keshuailiubiao"] = "人心已陷，如何固守？",

}

keshuaizhanghuan = sgs.General(extension, "keshuaizhanghuan", "qun", 4)

keshuaizhushou = sgs.CreateTriggerSkill{
    name = "keshuaizhushou",
	frequency = sgs.Skill_NotFrequent,
	priority = 2,
	events = {sgs.CardsMoveOneTime,sgs.EventPhaseChanging},
	can_trigger = function(self, target)
		return target 
	end,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) then
				--先发动效果，下面再清除
				for _, zh in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do  
					if zh:getMark("&keshuaizhushoulose-Clear") > 0 then
						local card_ids = room:getTag("keshuaizhushou_distag"):toIntList()
						--找到最大的点数
						local biggest_number = -1
						for _,id in sgs.qlist(card_ids) do
							local thecard = sgs.Sanguosha:getCard(id)
							if (thecard:getNumber() > biggest_number) then
								biggest_number = thecard:getNumber()
							end
						end
						local only_one_biggest = 0
						local disable_chooses = sgs.IntList()
						--最大的点数是否唯一
						for _,idd in sgs.qlist(card_ids) do
							if (room:getCardPlace(idd) ~= sgs.Player_DiscardPile) then continue end
							local thecard = sgs.Sanguosha:getCard(idd)
							if (thecard:getNumber() == biggest_number) then
								only_one_biggest = only_one_biggest + 1
								if only_one_biggest>1 then break end
							else
								--这些牌等会儿会显示成灰色，不能被选，让玩家看看有一个整体把握，看最大的牌是哪张
								--初版是没有限制“唯一”，是真的可以选的
								disable_chooses:append(idd)
							end
						end
						if only_one_biggest == 1 then
							room:fillAG(card_ids,zh,disable_chooses)
							local card_id = room:askForAG(zh, card_ids, true, self:objectName(),"keshuaizhushouagask")
			                if card_id>-1 then
								--检查有失去过此牌的角色
								local daplayers = sgs.SPlayerList()
								for _, dmd in sgs.qlist(room:getAllPlayers()) do  
									local dmdtag = dmd:getTag("keshuaizhushoutag"):toIntList()
									if dmdtag:contains(card_id) then
										daplayers:append(dmd)
									end
								end
								--这个时候可以反悔，不选择角色以取消伤害
								local sb = room:askForPlayerChosen(zh, daplayers, self:objectName(), "keshuaizhanghuan-ask", true, true)
								if sb then
									room:damage(sgs.DamageStruct(self:objectName(), zh, sb))
								end
							end
							room:clearAG(zh)
						end
					end
				end
				--开始清除
				for _,p in sgs.qlist(room:getAllPlayers()) do
					p:removeTag("keshuaizhushoutag")
				end
				room:removeTag("keshuaizhushou_distag")
			end
		elseif (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			--记录玩家失去的牌
			if move.from and (move.from:objectName() == player:objectName()) 
			and (move.from_places:contains(sgs.Player_PlaceEquip)
			or move.from_places:contains(sgs.Player_PlaceHand)) then
				room:setPlayerMark(player,"&keshuaizhushoulose-Clear",1)
				local tag = player:getTag("keshuaizhushoutag"):toIntList()
				for _,card_id in sgs.qlist(move.card_ids) do
					if not tag:contains(card_id) then
					    tag:append(card_id)
					end
				end
				local d = sgs.QVariant()
				d:setValue(tag)
				player:setTag("keshuaizhushoutag", d)
			end
			--记录进入弃牌堆的牌
			if (move.to_place == sgs.Player_DiscardPile) then
				local tag = room:getTag("keshuaizhushou_distag"):toIntList()
				for _,card_id in sgs.qlist(move.card_ids) do
					if not tag:contains(card_id) then
					    tag:append(card_id)
					end
				end
				local d = sgs.QVariant()
				d:setValue(tag)
				room:setTag("keshuaizhushou_distag", d)
			end
		end
	end
}
keshuaizhanghuan:addSkill(keshuaizhushou)

keshuaiyanggeCard = sgs.CreateSkillCard{
	name = "keshuaiyanggeCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		if #targets == 0 then
			return (to_select:objectName() ~= player:objectName())
			and to_select:hasSkill("keshuaiyangge")
		end
		return false
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:setPlayerMark(player,"&usedkeshuaiyangge_lun",1)
		local card_use = sgs.CardUseStruct()
		card_use.from = player
		card_use.to:append(target)
		card_use.card = sgs.Card_Parse("@MizhaoCard=.")
		room:useCard(card_use, true)  	   
		player:drawCards(1,self:getSkillName())
	end
}

keshuaiyanggeex = sgs.CreateZeroCardViewAsSkill{
	name = "keshuaiyanggeex&",
	enabled_at_play = function(self, player)
		--次数判断
		if (player:getMark("&usedkeshuaiyangge_lun") > 0) then
			return false
		end
		for _,p in sgs.qlist(player:getAliveSiblings()) do
			if (p:getMark("&usedkeshuaiyangge_lun") > 0) then
				return false
			end
		end
		--体力最低判断，只要有人比你低，你就不是最低
		for _,q in sgs.qlist(player:getAliveSiblings()) do
			if q:getHp() < player:getHp() then
				return false
			end
		end
		return player:getHandcardNum()>0
	end ,
	view_as = function()
		return keshuaiyanggeCard:clone()
	end
}
if not sgs.Sanguosha:getSkill("keshuaiyanggeex") then skills:append(keshuaiyanggeex) end

keshuaiyangge = sgs.CreateTriggerSkill{
    name = "keshuaiyangge",
	waked_skills = "mizhao",
	events = {sgs.EventPhaseStart,sgs.EventPhaseEnd,sgs.RoundEnd},
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseEnd) then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:hasSkill("keshuaiyanggeex",true) then
				    room:detachSkillFromPlayer(p, "keshuaiyanggeex",true,true,false)
				end
			end
		end
		if (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Play) then
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do
					if p:hasSkill(self:objectName(),true) then
						room:attachSkillToPlayer(player, "keshuaiyanggeex")
						break
					end
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
}
keshuaizhanghuan:addSkill(keshuaiyangge)

sgs.LoadTranslationTable{

	["keshuaizhanghuan"] = "张奂[衰]", 
	["&keshuaizhanghuan"] = "张奂",
	["#keshuaizhanghuan"] = "正身洁己",
	["designer:keshuaizhanghuan"] = "官方",
	["cv:keshuaizhanghuan"] = "官方",
	["illustrator:keshuaizhanghuan"] = "峰雨同程",

	["keshuaizhushou"] = "诛首",
	["keshuaizhushoulose"] = "诛首失去过牌",
	["keshuaizhushouagask"] = "诛首：请确认点数唯一最大的牌，或点击确定以取消",
	["keshuaizhanghuan-ask"] = "你可以对本回合失去过此牌的一名角色造成1点伤害",
	[":keshuaizhushou"] = "你失去过牌的一个回合结束时，若本回合置入弃牌堆的牌中有唯一点数最大且该牌在弃牌堆中，你可以对本回合失去过此牌的一名角色造成1点伤害。",

	["keshuaiyangge"] = "扬戈",
	["keshuaiyanggeex"] = "扬戈密诏",
	[":keshuaiyangge"] = "每轮限一次，体力值最低的其他角色可以于其出牌阶段对你发动“密诏”。",
	["usedkeshuaiyangge"] = "已发动扬戈",


	["$keshuaizhushou1"] = "",
	["$keshuaizhushou2"] = "",


	["~keshuaizhanghuan"] = "",
}

keshuaiyangqiu = sgs.General(extension, "keshuaiyangqiu", "qun", 4)

keshuaisaojianCard = sgs.CreateSkillCard{
	name = "keshuaisaojianCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		return (#targets < 1) 
		and not to_select:isKongcheng()
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]		
		local card_id = room:askForCardChosen(player, target, "h", self:getSkillName(),true)
		for _, p in sgs.qlist(room:getOtherPlayers(target)) do 
		    room:showCard(target,card_id,p,false)
		end
		for i = 1, 5 do
			if target:canDiscard(target, "h") then
				local dc = room:askForDiscard(target, self:getSkillName(), 1, 1)
				if dc:getEffectiveId() == card_id then
					break
				end
			end
		end
		if (target:getHandcardNum() > player:getHandcardNum()) then
			room:loseHp(player,1,true,player,self:getSkillName())
		end
	end
}
--主技能
keshuaisaojian = sgs.CreateViewAsSkill{
	name = "keshuaisaojian",
	n = 0,
	view_as = function(self, cards)
		return keshuaisaojianCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#keshuaisaojianCard")
	end, 
}
keshuaiyangqiu:addSkill(keshuaisaojian)

sgs.LoadTranslationTable{

	["keshuaiyangqiu"] = "阳球[衰]", 
	["&keshuaiyangqiu"] = "阳球",
	["#keshuaiyangqiu"] = "身蹈水火",
	["designer:keshuaiyangqiu"] = "官方",
	["cv:keshuaiyangqiu"] = "泪何不寐，小珂酱",
	["illustrator:keshuaiyangqiu"] = "鬼画府",

	["keshuaisaojian"] = "埽奸",
	[":keshuaisaojian"] = "出牌阶段限一次，你可以观看一名其他角色的手牌并向除该角色外的所有角色展示其中一张，该角色重复执行弃置一张手牌，直到其弃置了该牌或弃牌数达到五张，然后若其手牌数大于你，你失去1点体力。",

	["$keshuaisaojian2"] = "从实招来，免受皮肉之苦！",
	["$keshuaisaojian1"] = "阉竖当道，非酷刑不可服之！",


	["~keshuaiyangqiu"] = "只恨未扫尽奸宦，反受其害矣！",
}




keshuaidongzhuo = sgs.General(extension, "keshuaidongzhuo", "qun", 4)

keshuaiguanshiVS = sgs.CreateViewAsSkill{
	name = "keshuaiguanshi",
	n = 1,
	view_filter = function(self, selected, to_select)
        return (not sgs.Self:isLocked(to_select))
		and to_select:isKindOf("Slash")
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local suit = cards[1]:getSuit()
			local point = cards[1]:getNumber()
			local id = cards[1]:getId()
			local fireattack = sgs.Sanguosha:cloneCard("FireAttack", suit, point)
			fireattack:setSkillName("keshuaiguanshi")
			fireattack:addSubcard(id)
			return fireattack
		end
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("keshuaiguanshiused-PlayClear") == 0)
	end,
}

keshuaiguanshi = sgs.CreateTriggerSkill{
    name = "keshuaiguanshi",
	view_as_skill =  keshuaiguanshiVS,
	events = {sgs.PreCardUsed,sgs.Damage,sgs.CardEffect,sgs.PostCardEffected},
	can_trigger = function(self, target)
		return target 
	end,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.PostCardEffected) then
			local effect = data:toCardEffect()
		    if effect.card:getSkillName() == "keshuaiguanshi"
			and not effect.card:hasFlag("keshuaiguanshida") then
			    room:setCardFlag(effect.card,"keshuaiguanshijd")
		    end	
		end
		if (event == sgs.CardEffect) then
			local effect = data:toCardEffect()
			if (effect.card:getSkillName() == "keshuaiguanshi")
			and effect.card:hasFlag("keshuaiguanshijd") then
				local juedou = sgs.Sanguosha:cloneCard("duel")
				juedou:setSkillName(effect.card:getSkillName(false))
				juedou:addSubcard(effect.card)
				effect.card = juedou
				data:setValue(effect)
				juedou:deleteLater()
			end
		end
		if (event == sgs.Damage) then
			local damage = data:toDamage()
			if damage.card and (damage.card:getSkillName() == "keshuaiguanshi") then
				room:setCardFlag(damage.card,"keshuaiguanshida")
			end
		end
		if (event == sgs.PreCardUsed) then
			local use = data:toCardUse()
			if (use.card:getSkillName() == "keshuaiguanshi") then
				room:setPlayerMark(use.from,"keshuaiguanshiused-PlayClear",1)
			end
		end
	end
}
keshuaidongzhuo:addSkill(keshuaiguanshi)

keshuaicangxiong = sgs.CreateTriggerSkill{
	name = "keshuaicangxiong",
	events = {sgs.CardsMoveOneTime},
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() == player:objectName()
			and not move.from_places:contains(sgs.Player_PlaceJudge) and not move.from_places:contains(sgs.Player_PlaceDelayedTrick)
			and (bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD
			or (move.to_place == sgs.Player_PlaceHand and move.to:objectName() ~= move.from:objectName())) then
				for _, id in sgs.qlist(move.card_ids) do
					local cxcard = sgs.Sanguosha:getCard(id)
					player:setTag("keshuaicangxiongId",ToData(id))
					if player:askForSkillInvoke(self,KeToData("keshuaicangxiong-ask:"..cxcard:objectName())) then
						xumouCard(player,cxcard)
						if (player:getPhase() == sgs.Player_Play) then
							player:drawCards(1,self:objectName())
						end
					end
				end
			end
		end
	end,
}
keshuaidongzhuo:addSkill(keshuaicangxiong)

keshuaijiebing = sgs.CreatePhaseChangeSkill{
	name = "keshuaijiebing" ,
	frequency = sgs.Skill_Wake ,
	waked_skills = "keshuaibaowei",
	on_phasechange = function(self, player)
		local room = player:getRoom()
        room:sendCompulsoryTriggerLog(player, self)
		room:doSuperLightbox(player, "keshuaijiebing")
		room:setPlayerMark(player, self:objectName(), 1)
		if room:changeMaxHpForAwakenSkill(player,2) then
			room:recover(player, sgs.RecoverStruct(self:objectName(),player,2))
			room:acquireSkill(player, "keshuaibaowei")
		end
		return false
	end,
	can_trigger = function(self, target)
		if target and target:isAlive() and target:getPhase() == sgs.Player_Start
		and target:getMark(self:objectName())<1 and target:hasSkill(self:objectName()) then
			local n = 0
			for _,c in sgs.qlist(target:getJudgingArea())do
				if string.find(c:objectName(),"kehexumou")
				then n = n + 1 end
			end
			local Lord = target:getRoom():getLord()
			return Lord and Lord:getHp()<n or target:canWake(self:objectName())
		end
	end
}
keshuaidongzhuo:addSkill(keshuaijiebing)

keshuaibaowei = sgs.CreateTriggerSkill{
	name = "keshuaibaowei",
	events = {sgs.CardResponded,sgs.CardUsed,sgs.EventPhaseStart},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) 
		and player:hasSkill(self:objectName())
		and (player:getPhase() == sgs.Player_Finish) then
			local aps = sgs.SPlayerList()
			for _, dmd in sgs.qlist(room:getOtherPlayers(player)) do   
				if (dmd:getMark("&keshuaibaowei-Clear") > 0) then
					aps:append(dmd)
				end
			end
			if (aps:length() == 1) then
				room:sendCompulsoryTriggerLog(player, self)
				room:doAnimate(1, player:objectName(),aps:at(0):objectName())
				room:damage(sgs.DamageStruct(self:objectName(), player, aps:at(0), 2))
			elseif (aps:length() > 1) then 
				room:sendCompulsoryTriggerLog(player, self)
				room:loseHp(player,2,true,player,self:objectName())
			end
		end
		if (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if use.card:getTypeId()>0 then
				room:setPlayerMark(player,"&keshuaibaowei-Clear",1)
			end
		end
		if (event == sgs.CardResponded) then
			local response = data:toCardResponse()
			if response.m_card:getTypeId()>0 then
				room:setPlayerMark(player,"&keshuaibaowei-Clear",1)
			end
		end
	end,
	can_trigger = function(self, player)
		return player and player:isAlive()
	end,
}
if not sgs.Sanguosha:getSkill("keshuaibaowei") then skills:append(keshuaibaowei) end


sgs.LoadTranslationTable{

	["keshuaidongzhuo"] = "董卓[衰]", 
	["&keshuaidongzhuo"] = "董卓",
	["#keshuaidongzhuo"] = "华夏震栗",
	["designer:keshuaidongzhuo"] = "官方",
	["cv:keshuaidongzhuo"] = "官方",
	["illustrator:keshuaidongzhuo"] = "鬼画府",

	["keshuaiguanshi"] = "观势",
	[":keshuaiguanshi"] = "出牌阶段限一次，你可以将一张【杀】当无目标数限制的【火攻】使用，当此牌对一名角色结算结束时，若此牌没有造成过伤害，此牌对剩余角色以【决斗】效果结算。",

	["keshuaicangxiong"] = "藏凶",
	["keshuaicangxiong:keshuaicangxiong-ask"] = "你可以发动“藏凶”将这张【%src】蓄谋",
	[":keshuaicangxiong"] = "当你的牌被弃置或被其他角色获得后，你可以将此牌蓄谋，若此时为你的出牌阶段，你摸一张牌。",

	["keshuaijiebing"] = "劫柄",
	[":keshuaijiebing"] = "觉醒技，准备阶段，若你判定区内的蓄谋牌数量大于主公的体力值，你加2点体力上限并回复2点体力，然后获得“暴威”。",

	["keshuaibaowei"] = "暴威",
	[":keshuaibaowei"] = "锁定技，结束阶段，若本回合使用或打出过牌的其他角色的数量：等于1，你对其造成2点伤害；大于1，你失去2点体力。",

	["$keshuaiguanshi1"] = "挡我者死！",
	["$keshuaiguanshi2"] = "看尔等骄狂到几时！",
	["$keshuaicangxiong1"] = "汝甚得吾心，杀得好！",
	["$keshuaicangxiong2"] = "忠君护主，嗯，加官进爵！",
	["$keshuaijiebing1"] = "权势手中握，富贵梦里来。",
	["$keshuaijiebing2"] = "整个天下都要臣服于我！",
	["$keshuaibaowei1"] = "哪个敢反我？",
	["$keshuaibaowei2"] = "大汉天下，唯我独尊，哈哈哈哈哈哈哈！",

	["~keshuaidongzhuo"] = "胜者为王，败者为寇，我无话可说。",
}

keshuailuzhi = sgs.General(extension, "keshuailuzhi", "qun", 3)

keshuairuzong = sgs.CreateTriggerSkill{
	name = "keshuairuzong",
	events = {sgs.EventPhaseChanging, sgs.TargetSpecified},
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) then
				local theone = {}
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:getMark("keshuairuzonguse-Clear")>0 then
						table.insert(theone,p)
					end
				end
				if #theone==1 then
					if theone[1] == player then
						local canchooses = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getAllPlayers()) do
							if (p:getHandcardNum() < player:getHandcardNum()) then
								canchooses:append(p)
							end
						end
						local rzs = room:askForPlayersChosen(player, canchooses, self:objectName(), 0, 99, "keshuairuzongask", true, true)
						for _, rz in sgs.qlist(rzs) do
							local cha = player:getHandcardNum() - rz:getHandcardNum()
							rz:drawCards(cha,self:objectName())
						end
					else
						local cha = theone[1]:getHandcardNum() - player:getHandcardNum()
						if cha>0 and player:askForSkillInvoke(self,KeToData("keshuairuzong-ask:"..theone[1]:objectName())) then
							player:drawCards(cha,self:objectName())
						end
					end
				end
			end
		end
		if (event == sgs.TargetSpecified) then
			local use = data:toCardUse()
			if use.card:getTypeId()>0 then
				for _, t in sgs.qlist(use.to) do
					room:setPlayerMark(t,"keshuairuzonguse-Clear",1)
				end
			end
		end
	end
}
keshuailuzhi:addSkill(keshuairuzong)

keshuaidaorenCard = sgs.CreateSkillCard{
	name = "keshuaidaorenCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		return (#targets < 1) and (to_select:objectName() ~= player:objectName())
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:giveCard(player,target,self,self:getSkillName())
		local log = sgs.LogMessage()
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if player:inMyAttackRange(p) and target:inMyAttackRange(p) then
				log.to:append(p)
			end
		end
		log.type = "$keshuaidaorenlog"
		if (log.to:length() > 0) then
			room:sendLog(log)
		end
		for _, da in sgs.qlist(log.to) do
			room:damage(sgs.DamageStruct(self:getSkillName(), player, da, 1))
		end
	end
}

keshuaidaoren = sgs.CreateViewAsSkill{
    name = "keshuaidaoren",
    n = 1,
    view_filter = function(self, selected, to_select)
        return (not to_select:isEquipped())
    end,
    view_as = function(self, cards)
        if #cards > 0 then
            local card = keshuaidaorenCard:clone()
            for _,cc in ipairs(cards) do
                card:addSubcard(cc)
            end
            return card
        end
    end,
    enabled_at_play = function(self, player)
        return not player:hasUsed("#keshuaidaorenCard")
    end
}
keshuailuzhi:addSkill(keshuaidaoren)

sgs.LoadTranslationTable{

	["keshuailuzhi"] = "卢植[衰]", 
	["&keshuailuzhi"] = "卢植",
	["#keshuailuzhi"] = "眸宿渊渟",
	["designer:keshuailuzhi"] = "官方",
	["cv:keshuailuzhi"] = "官方",
	["illustrator:keshuailuzhi"] = "峰雨同程",

	["keshuairuzong"] = "儒宗",
	["keshuairuzongask"] = "你可以令任意名角色将手牌数摸至与你相同",
	["keshuairuzong:keshuairuzong-ask"] = "你可以发动“儒宗”将手牌摸至与 %src 相同",

	[":keshuairuzong"] = "回合结束时，若你本回合使用牌仅指定过一名角色为目标，若这名角色：是你，你可以令任意名其他角色将手牌数摸至与你相同；不是你，你可以将手牌数摸至与其相同。",

	["keshuaidaoren"] = "蹈刃",
	["$keshuaidaorenlog"] = "满足<font color='yellow'><b>“蹈刃”</s></font>条件的角色有 %to",
	[":keshuaidaoren"] = "出牌阶段限一次，你可以交给一名角色一张手牌，然后你对你与其攻击范围内均包含的角色各造成1点伤害。",

	["$keshuairuzong1"] = "抱才育器，以效国家！",
	["$keshuairuzong2"] = "内举不避亲，外举不避仇！",
	["$keshuaidaoren1"] = "无君无父之辈，怎敢在此造次！",
	["$keshuaidaoren2"] = "召虎狼以平乱，无异饮鸩止渴！",

	["~keshuailuzhi"] = "朝廷重用这种人等，怎会不败...",
	
}

keshuaisonghuanghou = sgs.General(extension, "keshuaisonghuanghou", "qun", 3,false)

keshuaizhongzen = sgs.CreateTriggerSkill{
	name = "keshuaizhongzen",
	events = {sgs.EventPhaseStart,sgs.EventPhaseEnd,sgs.CardsMoveOneTime},
	frequency = sgs.Skill_NotFrequent, 
	can_trigger = function(self, target)
		return target and target:isAlive()
		and target:getPhase() == sgs.Player_Discard
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() == player:objectName() and move.to_place == sgs.Player_DiscardPile
			and bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
				local tag = player:getTag("keshuaizhongzentag"):toIntList()
				for _,id in sgs.qlist(move.card_ids) do
					if not tag:contains(id) then
						tag:append(id)
					end
				end
				local d = sgs.QVariant()
				d:setValue(tag)
				player:setTag("keshuaizhongzentag", d)
			end
		end
		if event == sgs.EventPhaseStart and player:hasSkill(self) then
			local tos = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:getHandcardNum() < player:getHandcardNum() then
					tos:append(p)
				end
			end
			if tos:length()>0 then
				room:sendCompulsoryTriggerLog(player,self)
			end
			for _, p in sgs.qlist(tos) do
				if p:getHandcardNum()>0 then
					local card = room:askForExchange(p, self:objectName(), 1, 1, false, "keshuaizhongzen_give:"..player:objectName())
					if card then
						room:giveCard(p, player, card, self:objectName())
					end
				end
			end
		end
		if event == sgs.EventPhaseEnd then
			local spades = 0
			for _,id in sgs.qlist(player:getTag("keshuaizhongzentag"):toIntList()) do
				if sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Spade then
					spades = spades + 1
				end
			end
			player:removeTag("keshuaizhongzentag")
			if spades > player:getHp() and player:hasSkill(self) then
				room:sendCompulsoryTriggerLog(player,self)
				player:throwAllHandCardsAndEquips(self:objectName())
			end
		end
	end
}
keshuaisonghuanghou:addSkill(keshuaizhongzen)

keshuaixuchong = sgs.CreateTriggerSkill{
	name = "keshuaixuchong",
	events = {sgs.TargetConfirmed},
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetConfirmed) then
			local use = data:toCardUse()
			if (not use.card:isKindOf("SkillCard"))
			and use.to:contains(player) and player:hasSkill(self:objectName()) then
				if player:askForSkillInvoke(self:objectName(), data) then
					if room:askForChoice(player,self:objectName(),"draw+add") == "draw" then
						player:drawCards(1,self:objectName())
					else
						local log = sgs.LogMessage()
						log.type = "$keshuaixuchongaddlog"
						log.from = room:getCurrent()
						room:sendLog(log)
						room:addMaxCards(room:getCurrent(), 2, true)
					end
					for _,id in sgs.qlist(sgs.Sanguosha:getRandomCards(true)) do
						if sgs.Sanguosha:getEngineCard(id):isKindOf("KezhuanYing")
						and room:getCardOwner(id) == nil then
							player:obtainCard(sgs.Sanguosha:getCard(id), true)
							break
						end
					end
				end
			end
		end
	end
}
keshuaisonghuanghou:addSkill(keshuaixuchong)

sgs.LoadTranslationTable{

	["keshuaisonghuanghou"] = "宋皇后[衰]", 
	["&keshuaisonghuanghou"] = "宋皇后",
	["#keshuaisonghuanghou"] = "兰心蕙质",
	["designer:keshuaisonghuanghou"] = "官方",
	["cv:keshuaisonghuanghou"] = "官方",
	["illustrator:keshuaisonghuanghou"] = "峰雨同程",

	["keshuaizhongzen"] = "众谮",
	[":keshuaizhongzen"] = "锁定技，弃牌阶段开始时，手牌数小于你的角色各交给你一张手牌；弃牌阶段结束时，若你本阶段弃置的♠牌的数量大于体力值，你弃置所有牌。",
	["keshuaizhongzen_give"] = "众谮：请交给 %src 一张手牌",

	["keshuaixuchong"] = "虚宠",
	["$keshuaixuchongaddlog"] = "%from 本回合的手牌上限+2",
	["keshuaixuchong:draw"] = "摸一张牌",
	["keshuaixuchong:add"] = "当前回合角色本回合的手牌上限+2",
	[":keshuaixuchong"] = "当你成为牌的目标后，你可以摸一张牌或令当前回合角色本回合的手牌上限+2，然后你从游戏外获得一张【影】。",

	["$keshuaizhongzen1"] = "",
	["$keshuaizhongzen2"] = "",
	["$keshuaixuchong1"] = "",
	["$keshuaixuchong2"] = "",

	["~keshuaisonghuanghou"] = "",
}

keshuaichenfan = sgs.General(extension, "keshuaichenfan", "qun", 3)

keshuaigangfen = sgs.CreateTriggerSkill{
	name = "keshuaigangfen",
	events = {sgs.TargetSpecifying},
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetSpecifying) then
			local use = data:toCardUse()
			if (use.card:isKindOf("Slash")) then
				room:setTag("keshuaigangfenData",data)
				for _,cf in sgs.qlist(room:findPlayersBySkillName(self:objectName()))do
					if use.to:contains(cf) then continue end
					if use.from:getHandcardNum() > cf:getHandcardNum()
					and cf:askForSkillInvoke(self,KeToData("keshuaigangfenask:"..use.from:objectName())) then
						use.to:append(cf)
						local log = sgs.LogMessage()
						log.card_str = use.card:toString()
						log.type = "#keshuaigangfenask2"
						log.from = cf
						room:sendLog(log)
						for _,oth in sgs.qlist(room:getOtherPlayers(use.from)) do
							if use.to:contains(oth) then continue end
							if oth:askForSkillInvoke(self,KeToData("keshuaigangfenask2:"..cf:objectName()),false) then
								use.to:append(oth)
								log.from = oth
								room:sendLog(log)
							end
						end
						room:showAllCards(use.from)
						local blacks = 0
						for _,c in sgs.qlist(use.from:getCards("h")) do
							if c:isBlack() then blacks = blacks+1 end
						end
						if (blacks < use.to:length()) then
							use.to = sgs.SPlayerList()
						end
						room:sortByActionOrder(use.to)
						data:setValue(use)
					end
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
}
keshuaichenfan:addSkill(keshuaigangfen)


keshuaidangrenVS = sgs.CreateViewAsSkill{
	name = "keshuaidangren",
	n = 0,
	view_filter = function(self, selected, to_select)
		return false
	end ,
	view_as = function(self, cards)
		local pichi = sgs.Sanguosha:cloneCard("Peach")
		pichi:setSkillName("keshuaidangren")
		return pichi
	end,
	enabled_at_play = function(self, player)
		local pichi = sgs.Sanguosha:cloneCard("Peach")
		pichi:setSkillName("keshuaidangren")
		pichi:deleteLater()
		return pichi:isAvailable(player)
		and player:getChangeSkillState("keshuaidangren")==1
	end
}

keshuaidangren = sgs.CreateTriggerSkill{
	name = "keshuaidangren",
	change_skill = true,
	view_as_skill = keshuaidangrenVS,
	events = {sgs.AskForPeaches,sgs.CardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.AskForPeaches) then
			local dying = data:toDying()
			if (dying.who:objectName() ~= player:objectName())
			and (player:getChangeSkillState("keshuaidangren") == 2) then
				room:sendCompulsoryTriggerLog(player, "keshuaidangren")
				local pichi = sgs.Sanguosha:cloneCard("Peach")
				pichi:setSkillName("_keshuaidangren")
				room:useCard(sgs.CardUseStruct(pichi,player,dying.who), false)
				pichi:deleteLater() 
			elseif (dying.who:objectName() == player:objectName())
			and (player:getChangeSkillState("keshuaidangren") == 1) then
				if player:askForSkillInvoke(self,KeToData("keshuaidangrenself"),false) then
					local pichi = sgs.Sanguosha:cloneCard("Peach")
					pichi:setSkillName("keshuaidangren")
					room:useCard(sgs.CardUseStruct(pichi,player), false)
					pichi:deleteLater() 
				end
			end
		elseif (event == sgs.CardUsed) then
			local use = data:toCardUse()
			if (use.card:getSkillName() == self:objectName()) then
				if (player:getChangeSkillState("keshuaidangren") == 1) then
				    room:setChangeSkillState(player, "keshuaidangren", 2)
				elseif (player:getChangeSkillState("keshuaidangren") == 2) then
					room:setChangeSkillState(player, "keshuaidangren", 1)
				end
			end
		end
	end,
}
keshuaichenfan:addSkill(keshuaidangren)

sgs.LoadTranslationTable{

	["keshuaichenfan"] = "陈蕃[衰]", 
	["&keshuaichenfan"] = "陈蕃",
	["#keshuaichenfan"] = "不畏强御",
	["designer:keshuaichenfan"] = "官方",
	["cv:keshuaichenfan"] = "官方",
	["illustrator:keshuaichenfan"] = "峰雨同程",

	["keshuaigangfen"] = "刚忿",
	["keshuaigangfen:keshuaigangfenask"] = "你可以对 %src 发动“刚忿”成为此【杀】的额外目标",
	["keshuaigangfen:keshuaigangfenask2"] = "%src 发动了“刚忿”，你可以成为此【杀】的额外目标",
	[":keshuaigangfen"] = "当手牌数大于你的角色使用【杀】指定目标后，若目标不包括你，你可以成为此【杀】的额外目标，且其他非目标角色也可以如此做，然后使用者展示所有手牌，若其中黑色牌小于目标数，取消此【杀】的所有目标。",
	["#keshuaigangfenask2"] = "%from 选择成为此 %card 的目标",

	["keshuaidangren"] = "当仁",
	[":keshuaidangren"] = "转换技，①当你需要对你使用【桃】时，你可以视为使用之；②当其他角色处于濒死状态时，你视为对其使用一张【桃】。",
	[":keshuaidangren1"] = "转换技，①当你需要对你使用【桃】时，你可以视为使用之；<font color=\"#01A5AF\"><s>②当其他角色处于濒死状态时，你视为对其使用一张【桃】</s></font>。",
	[":keshuaidangren2"] = "转换技，<font color=\"#01A5AF\"><s>①当你需要对你使用【桃】时，你可以视为使用之</s></font>；②当其他角色处于濒死状态时，你视为对其使用一张【桃】。",
	["keshuaidangren:keshuaidangrenself"] = "你可以发动“当仁”，视为使用【桃】",

	["$keshuaizhongzen1"] = "",
	["$keshuaizhongzen2"] = "",
	["$keshuaixuchong1"] = "",
	["$keshuaixuchong2"] = "",

	["~keshuaichenfan"] = "",
}

keshuaizhangju = sgs.General(extension, "keshuaizhangju", "qun", 4)

keshuaiqiluanCard = sgs.CreateSkillCard{
	name = "keshuaiqiluanCard",
	filter = function(self, targets, to_select, player)
		local pattern = self:getUserString()
		local slash = dummyCard(pattern)
		if not slash or slash:targetFixed()
		then return false end
		local plist = sgs.PlayerList()
		for i = 1, #targets do
			plist:append(targets[i])
		end
		return slash:targetFilter(plist, to_select, player)
	end,
	feasible = function(self,targets)
		local pattern = self:getUserString()
		local slash = dummyCard(pattern)
		return slash and (slash:targetFixed() or #targets>0)
	end,
	on_validate_in_response = function(self,from)
		local room = from:getRoom()
		local num = self:subcardsLength()
		local fris = room:askForPlayersChosen(from, room:getOtherPlayers(from), self:getSkillName(), 1, num, "keshuaiqiluan_ask_slash", true, true)
		room:throwCard(self,self:getSkillName(),from)
		local pattern = self:getUserString()
		room:addPlayerMark(from,"keshuaiqiluanUse-Clear")
		for _, fri in sgs.qlist(fris) do
			local sha = room:askForCard(fri,pattern,"keshuaiqiluan-slash", ToData(from),sgs.Card_MethodResponse,from,false,"",true)
			if sha then
				from:drawCards(num,self:getSkillName())
				return sha
			end
		end
		return nil
	end,
	on_validate = function(self,use)
		local room = use.from:getRoom()
		local num = self:subcardsLength()
		local fris = room:askForPlayersChosen(use.from, room:getOtherPlayers(use.from), self:getSkillName(), 1, num, "keshuaiqiluan_ask_slash", true, true)
		room:throwCard(self,self:getSkillName(),use.from)
		local pattern = self:getUserString()
		room:addPlayerMark(use.from,"keshuaiqiluanUse-Clear")
		for _, fri in sgs.qlist(fris) do
			local sha = room:askForCard(fri,pattern,"keshuaiqiluan-slash", ToData(use.from),sgs.Card_MethodResponse,use.from,false,"",true)
			if sha then
				use.from:drawCards(num,self:getSkillName())
				return sha
			end
		end
		return nil
	end,
}
keshuaiqiluan = sgs.CreateViewAsSkill{
	name = "keshuaiqiluan",
	n = 999,
	response_or_use = true,
	view_filter = function(self, selected, to_select)
        return not sgs.Self:isJilei(to_select)
	end,
	view_as = function(self, cards)
		if #cards >= 1 then
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern=="" then pattern = "slash" end
			local card = keshuaiqiluanCard:clone()
			card:setUserString(pattern)
			for _,c in pairs(cards) do
				card:addSubcard(c)
			end
			return card
		end
	end,
	enabled_at_play = function(self, player)
		return player:getMark("keshuaiqiluanUse-Clear")<2
		and sgs.Slash_IsAvailable(player)
	end, 
	enabled_at_response = function(self, player, pattern)
		return (pattern == "slash" or pattern == "jink") and player:getMark("keshuaiqiluanUse-Clear")<2
		and sgs.Sanguosha:getCurrentCardUseReason()==sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
	end
}
keshuaizhangju:addSkill(keshuaiqiluan)

--[[keshuaiqiluanCard = sgs.CreateSkillCard{
	name = "keshuaiqiluanCard",
	filter = function(self, targets, to_select, player)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		local plist = sgs.PlayerList()
		for i = 1, #targets, 1 do
			plist:append(targets[i])
		end
		return slash:targetFilter(plist, to_select, sgs.Self)
	end,
	on_use = function(self, room, player, targets)
		local num = self:getSubcards():length()
		local slashtars = sgs.SPlayerList()
		for _, p in ipairs(targets) do
			slashtars:append(p)
		end
		local fris = room:askForPlayersChosen(player, room:getOtherPlayers(player), self:objectName(), 0, num, "keshuaiqiluanplayerask_slash", true, true)
		for _, fri in sgs.qlist(fris) do	
			local sha = room:askForExchange(player, self:objectName(), 1, 1, false, "keshuaiqiluan-slash",true,"Slash")
			--local sha = room:askForCard(fri,"slash","keshuaiqiluan-slash", data,sgs.Card_MethodResponse)
			if sha then
				local slash = sgs.Sanguosha:cloneCard("slash")
				slash:addSubcard(sha)
				slash:setSkillName("_keshuaiqiluan")
				local card_use = sgs.CardUseStruct()
				card_use.from = player
				card_use.to = slashtars
				card_use.card = slash
				room:useCard(card_use, true)
				slash:deleteLater()  
			end
		end
	end
}

keshuaiqiluanVS = sgs.CreateViewAsSkill{
	name = "keshuaiqiluan",
	n = 999,
	response_or_use = true,
	view_filter = function(self, selected, to_select)
        return not sgs.Self:isJilei(to_select)
	end,
	view_as = function(self, cards)
		if #cards >= 1 then
			local card = keshuaiqiluanCard:clone()
			for _,c in pairs(cards) do
				card:addSubcard(c)
			end
			return card
		else
			return nil
		end
	end,
	enabled_at_play = function(self, player)
		return not (player:hasUsed("#keshuaiqiluanCard")) 
	end, 
	enabled_at_response = function(self, player, pattern)
		return (pattern == "slash")
	end
}

keshuaiqiluan = sgs.CreateTriggerSkill{
	name = "keshuaiqiluan",
	events = {sgs.CardAsked},
	view_as_skill = keshuaiqiluanVS,
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardAsked) then
			local pattern = data:toStringList()
			if (pattern[1] == "jink") then 
				local xxx = room:askForDiscard(player, self:objectName(), 999, 0, true, true, "keshuaiqiluanask")
				if xxx then
					local fris = room:askForPlayersChosen(player, room:getOtherPlayers(player), self:objectName(), 0, xxx:getSubcards():length(), "keshuaiqiluanplayerask_jink", true, true)
					for _, fri in sgs.qlist(fris) do	
						local shan = room:askForCard(fri,"jink","keshuaiqiluan-jink", data,sgs.Card_MethodResponse)
						if shan then
							room:provide(shan)
							player:drawCards(xxx:getSubcards():length(),self:objectName())
							return true
						end
					end
				end
			end
			if (pattern[3]=="response") and string.find(pattern[1], "slash") then 
				local xxx = room:askForDiscard(player, self:objectName(), 999, 0, true, true, "keshuaiqiluanask")
				if xxx then
					local fris = room:askForPlayersChosen(player, room:getOtherPlayers(player), self:objectName(), 0, xxx:getSubcards():length(), "keshuaiqiluanplayerask_slash", true, true)
					for _, fri in sgs.qlist(fris) do	
						local sha = room:askForCard(fri,"slash","keshuaiqiluan-slash", data,sgs.Card_MethodResponse)
						if sha then
							room:provide(sha)
							player:drawCards(xxx:getSubcards():length(),self:objectName())
							return true
						end
					end
				end
			end
		end
	end
}
keshuaizhangju:addSkill(keshuaiqiluan)]]

keshuaixiangjiaVS = sgs.CreateZeroCardViewAsSkill{
	name = "keshuaixiangjia",
	enabled_at_play = function(self, player)
		return player:getWeapon() and player:getMark("keshuaixiangjiaUse-Clear")<1
	end ,
	view_as = function()
		local jdsr = sgs.Sanguosha:cloneCard("Collateral")
		jdsr:setSkillName("keshuaixiangjia")
		return jdsr
	end
}

keshuaixiangjia = sgs.CreateTriggerSkill{
	name = "keshuaixiangjia",
	view_as_skill = keshuaixiangjiaVS,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardFinished},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardFinished then
			local use = data:toCardUse()
			if (use.card:getSkillName() == "keshuaixiangjia")
			and use.card:isKindOf("Collateral")  then
				room:addPlayerMark(player,"keshuaixiangjiaUse-Clear")
				local slashones = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if use.from:canSlash(p) then
						slashones:append(p)
					end
				end
				--local eny = room:askForPlayerChosen(use.to:at(0), slashones, self:objectName(), "keshuaixiangjia-ask", true, false)
				if slashones:length()>0 and use.to:at(0):askForSkillInvoke(self,data,false) then
				--if eny then
					--use.from:setTag("collateralVictim",ToData(eny))
					local jdsr = sgs.Sanguosha:cloneCard("Collateral")
					jdsr:setSkillName("_keshuaixiangjia")
					local card_use = sgs.CardUseStruct()
					card_use.from = use.to:at(0)
					card_use.to:append(use.from)
					card_use.card = jdsr
					room:useCard(card_use, false)
					jdsr:deleteLater()
				end
			end
		end
	end,
	--[[can_trigger = function(self,target)
		return target
	end]]
}
keshuaizhangju:addSkill(keshuaixiangjia)

sgs.LoadTranslationTable{

	["keshuaizhangju"] = "张举[衰]", 
	["&keshuaizhangju"] = "张举",
	["#keshuaizhangju"] = "草头天子",
	["designer:keshuaizhangju"] = "官方",
	["cv:keshuaizhangju"] = "官方",
	["illustrator:keshuaizhangju"] = "峰雨同程",

	["keshuaiqiluan"] = "起乱",
	["keshuaiqiluan_ask_slash"] = "你可以令等量的角色选择是否打出一张【杀】（【闪】）",
	["keshuaiqiluan-slash"] = "你可以打出一张【杀】令其使用之",
	[":keshuaiqiluan"] = "每回合限两次，当你需要使用【杀】/【闪】时，你可以弃置任意张牌并令至多X名角色选择是否打出一张【杀】/【闪】直到有角色响应（X为你弃置的牌数），你摸X张牌并使用此牌。",

	["keshuaixiangjia"] = "相假",
	[":keshuaixiangjia"] = "出牌阶段限一次，若你的装备区内有武器牌，你可以视为对一名角色使用【借刀杀人】，此牌结算后，其可以视为对你使用一张【借刀杀人】。",

	["$keshuaiqiluan1"] = "",
	["$keshuaiqiluan2"] = "",
	["$keshuaixiangjia1"] = "",
	["$keshuaixiangjia2"] = "",

	["~keshuaizhangju"] = "",
}



keshuaicaojiewangfu = sgs.General(extension, "keshuaicaojiewangfu", "qun", 3)

keshuaizonghai = sgs.CreateTriggerSkill{
	name = "keshuaizonghai",
    frequency = sgs.Skill_NotFrequent,
	events = {sgs.EnterDying,sgs.QuitDying},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.QuitDying) then
			local dying = data:toDying()
			for _, dmd in sgs.qlist(room:getAllPlayers()) do 
				for _, p in sgs.qlist(room:getAllPlayers()) do 
					if dmd:getMark("&keshuaizonghai+#"..p:objectName())>0 then
						room:setPlayerMark(dmd,"&keshuaizonghai+#"..p:objectName(),0)
						room:damage(sgs.DamageStruct(self:objectName(),p,dmd))
					end
				end
				if dmd:hasFlag("keshuaizonghai") then
					dmd:setFlags("-keshuaizonghai")
					room:removePlayerCardLimitation(dmd,"use",".")
				end
			end
		end
		if (event == sgs.EnterDying) then
			local dying = data:toDying()
			for _, cw in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do 
				if cw:getMark("&usedkeshuaizonghai_lun")<1
				and cw~=dying.who and dying.who:isAlive() then
					local to_data = sgs.QVariant()
					to_data:setValue(dying.who)
					if cw:askForSkillInvoke(self, to_data) then
						room:setPlayerMark(cw,"&usedkeshuaizonghai_lun",1)
						local daomeidans = room:askForPlayersChosen(dying.who, room:getAllPlayers(), self:objectName(), 0, 2, "keshuaizonghai-ask", false, true)
						for _,dmd in sgs.qlist(room:getAllPlayers()) do 
							if daomeidans:contains(dmd) then
								room:doAnimate(1,dying.who:objectName(),dmd:objectName())
								room:setPlayerMark(dmd,"&keshuaizonghai+#"..cw:objectName(),1)
							else
								dmd:setFlags("keshuaizonghai")
								room:setPlayerCardLimitation(dmd,"use",".",false)
							end
						end
					end
				end
			end
		end
	end ,
	can_trigger = function(self, player)
		return player
	end,
}
keshuaicaojiewangfu:addSkill(keshuaizonghai)

keshuaijueyin = sgs.CreateTriggerSkill{
	name = "keshuaijueyin",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged,sgs.ConfirmDamage},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.ConfirmDamage) then
			local damage = data:toDamage()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if (p:getMark("&keshuaijueyinda-Clear") > 0) then
					room:sendCompulsoryTriggerLog(p,self)
					damage.damage = damage.damage + 1
				end
			end
			data:setValue(damage)
		end
		if (event == sgs.Damaged) 
		and player:hasSkill(self:objectName())
		and player:getMark("keshuaijueyin-Clear")<1 then
			room:addPlayerMark(player,"keshuaijueyin-Clear")
			if room:askForSkillInvoke(player, self:objectName(), data) then
				room:setPlayerMark(player,"&keshuaijueyinda-Clear",1)
				player:drawCards(3,self:objectName())
			end
		end
	end,
	can_trigger = function(self,target)
		return target and target:isAlive()
	end
}
keshuaicaojiewangfu:addSkill(keshuaijueyin)

sgs.LoadTranslationTable{

	["keshuaicaojiewangfu"] = "曹节＆王甫[衰]", 
	["&keshuaicaojiewangfu"] = "曹节＆王甫",
	["#keshuaicaojiewangfu"] = "独乱海内",
	["designer:keshuaicaojiewangfu"] = "官方",
	["cv:keshuaicaojiewangfu"] = "官方",
	["illustrator:keshuaicaojiewangfu"] = "峰雨同程",

	["keshuaizonghai"] = "纵害",
	["usedkeshuaizonghai"] = "已使用纵害",
	["keshuaizonghai-ask"] = "请选择至多两名角色（只有这些角色才能使用牌直到你脱离濒死状态）",
	[":keshuaizonghai"] = "每轮限一次，当其他角色进入濒死状态时，你可以令其选择至多两名角色，没有被选择的角色不能使用牌直到其脱离濒死状态后，你对其选择的角色各造成1点伤害。",

	["keshuaijueyin"] = "绝禋",
	["keshuaijueyinda"] = "绝禋伤害",
	[":keshuaijueyin"] = "当你每回合首次受到伤害后，你可以摸三张牌，然后本回合所有角色受到的伤害+1。",

	["$keshuaiqiluan1"] = "",
	["$keshuaiqiluan2"] = "",
	["$keshuaixiangjia1"] = "",
	["$keshuaixiangjia2"] = "",

	["~keshuaizhangju"] = "",
}










sgs.Sanguosha:addSkills(skills)
return {extension}

