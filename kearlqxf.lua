--==《新武将》==--
extension = sgs.Package("kearlqxf", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

function KeToData(self)
	local data = sgs.QVariant()
	if type(self)=="string"
	or type(self)=="boolean"
	or type(self)=="number"
	then data = sgs.QVariant(self)
	elseif self~=nil then data:setValue(self) end
	return data
end

--buff集中
kelqxfslashmore = sgs.CreateTargetModSkill{
	name = "kelqxfslashmore",
	pattern = ".",
	residue_func = function(self, from, card, to)
		local n = 0
		if card:getSkillName()=="kelqjuesui" and from:hasSkill("kelqjuesuiUse") then
			n = n + 1000
		end
		return n
	end,
	extra_target_func = function(self, from, card)
		local n = 0
		if card:isKindOf("Slash") and (card:getSuit() == sgs.Card_Heart) and from:hasSkill("kelqjunshen") then
			n = n + 1
		end
		--[[if from:hasSkill("keshuaiguanshi")
		and card:isKindOf("FireAttack") 
		and (card:getSkillName() == "keshuaiguanshi") then
			n = n + 1000
		end]]
		return n
	end,
	distance_limit_func = function(self, from, card, to)
		local n = 0
		if card:isKindOf("Slash") and card:getSuit() == sgs.Card_Diamond and from:hasSkill("kelqjunshen") then
			n = n + 1000
		end
		--[[if card:isKindOf("Slash") and card:getSkillName() == "_keshuaiyansha" then
			n = n + 1000
		end]]
		return n
	end
}
if not sgs.Sanguosha:getSkill("kelqxfslashmore") then skills:append(kelqxfslashmore) end


kelqguanyu = sgs.General(extension, "kelqguanyu", "shu", 4)

kelqchaojue = sgs.CreateTriggerSkill{
	name = "kelqchaojue",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart,sgs.EventPhaseChanging,sgs.Death},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
	    if (event == sgs.Death) then
			local death = data:toDeath()
			local pattern = death.who:getTag("kelqchaojueLimitation"):toString()
			death.who:removeTag("kelqchaojueLimitation")
			for _, p in sgs.qlist(room:getAllPlayers()) do 
				if pattern~="" then
					room:removePlayerCardLimitation(p,"use,response",pattern)
				end
				local n = p:getMark(death.who:objectName().."kelqchaojue-Clear")
				if n > 0 then
					room:setPlayerMark(p,death.who:objectName().."kelqchaojue-Clear",0)
					room:removePlayerMark(p,"@skill_invalidity",n)
				end
			end
		end
		if (event == sgs.EventPhaseChanging) then
		    local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) then
				local pattern = player:getTag("kelqchaojueLimitation"):toString()
				player:removeTag("kelqchaojueLimitation")
				for _, p in sgs.qlist(room:getAllPlayers()) do 
					if pattern~="" then
						room:removePlayerCardLimitation(p,"use,response",pattern)
					end
					local n = p:getMark(player:objectName().."kelqchaojue-Clear")
					if n > 0 then
						room:setPlayerMark(p,player:objectName().."kelqchaojue-Clear",0)
						room:removePlayerMark(p,"@skill_invalidity",n)
					end
				end
			end
		end
		if event == sgs.EventPhaseStart
        and player:getPhase() == sgs.Player_Start
        and player:isAlive() and player:hasSkill(self) then
            local discid = room:askForDiscard(player, self:objectName(), 1, 1, true, false, "kelqchaojueask", ".", self:objectName())
			if discid then
				room:broadcastSkillInvoke(self:objectName())
				room:setPlayerMark(player,"&kelqchaojue+:+"..discid:getSuitString().."_char-Clear",1)
				player:setTag("kelqchaojueLimitation",ToData(".|"..discid:getSuitString()))
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do
					room:setPlayerCardLimitation(p,"use,response",".|"..discid:getSuitString(),false)
					local todis = room:askForExchange(p, "kelqchaojue_show", 1, 1, false, "kelqchaojue_show:"..player:objectName().."::"..discid:getSuitString(),true, ".|"..discid:getSuitString())
					if todis then
						room:showCard(p,todis:getEffectiveId())
						room:giveCard(p,player,todis,self:objectName())
					else
						room:addPlayerMark(p,player:objectName().."kelqchaojue-Clear")
						room:addPlayerMark(p,"@skill_invalidity")
					end
				end  	    
            end					
		end
	end,
	can_trigger = function(self, player)
	    return player
	end,
}
kelqguanyu:addSkill(kelqchaojue)

kelqjunshenVS = sgs.CreateOneCardViewAsSkill{
	name = "kelqjunshen",
	response_or_use = true,
	view_filter = function(self, card)
		if not card:isRed() then return false end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash")
			slash:setSkillName("kelqjunshen")
			slash:addSubcard(card)
			slash:deleteLater()
			return slash:isAvailable(sgs.Self)
		end
		return true
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("kelqjunshen")
		slash:addSubcard(card)
		return slash
	end,
	enabled_at_play = function(self, player)
		return sgs.Slash_IsAvailable(player)
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "slash"
	end
}

kelqjunshen = sgs.CreateTriggerSkill{
	name = "kelqjunshen",
	view_as_skill = kelqjunshenVS,
	events = {sgs.DamageCaused},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageCaused) then
			local damage = data:toDamage()
			if damage.card and damage.card:getSkillName() == "kelqjunshen" then
				if damage.to:canDiscard(damage.to,"e") and room:askForChoice(damage.to,"kelqjunshen","qizhi+jiashang")=="qizhi" then
					damage.to:throwAllEquips("kelqjunshen")
				else
					damage.damage = damage.damage + 1
					data:setValue(damage)
				end
			end
		end	
	end,
}
kelqguanyu:addSkill(kelqjunshen)

sgs.LoadTranslationTable{
    ["kearlqxf"] = "龙起襄樊",

	["kelqguanyu"] = "关羽[龙]", 
	["&kelqguanyu"] = "关羽",
	["#kelqguanyu"] = "国士无双",
	["designer:kelqguanyu"] = "官方",
	["cv:kelqguanyu"] = "官方",
	["illustrator:kelqguanyu"] = "鬼画府",

	["kelqchaojue"] = "超绝",
	["kelqchaojueask"] = "超绝：你可以弃置一张手牌令所有其他角色本回合不能使用或打出与之花色相同的牌",
	[":kelqchaojue"] = "准备阶段，你可以弃置一张手牌令所有其他角色本回合不能使用或打出与之花色相同的牌，然后这些角色依次选择一项：1.展示并交给你一张与此牌花色相同的手牌；2.其本回合所有非锁定技失效。",
	["kelqchaojue_show"] = "超绝：你展示并交给%src一张%arg手牌；否则你本回合所有非锁定技失效",

	["kelqjunshen"] = "军神",
	["kelqjunshen:qizhi"] = "弃置装备区的所有牌",
	["kelqjunshen:jiashang"] = "此伤害+1",
	[":kelqjunshen"] = "你可以将一张红色牌当【杀】使用或打出，当你以此法使用的【杀】对一名角色造成伤害时，其选择一项：1.弃置装备区里的所有牌；2.令此伤害+1。你使用的♦【杀】无距离限制、♥【杀】的目标数限制+1。",


	["$kelqchaojue1"] = "逃归君父，振古通义。",
	["$kelqchaojue2"] = "同休等戚，祸福共之。",
	["$kelqjunshen1"] = "将帅讲武，习射御角力！",
	["$kelqjunshen2"] = "万众之中，斩汝首而还。",

	["~kelqguanyu"] = "良将不怯死以苟免。",
}

kelqcaoren = sgs.General(extension, "kelqcaoren", "wei", 4)

kelqlizhongcard = sgs.CreateSkillCard{
	name = "kelqlizhongcard",
	will_throw = false,
	filter = function(self,targets,to_selec,source)
		if self:subcardsLength()>0 then
			if self:subcardsLength()>#targets then
				local id = self:getSubcards():at(#targets)
				local n = sgs.Sanguosha:getCard(id):getRealCard():toEquipCard():location()
				return to_selec:hasEquipArea(n) and to_selec:getEquip(n)==nil
			end
		else
			if #targets>0 then
				return targets[1]:hasEquip() and to_selec:hasEquip()
			else
				return to_selec==source
				or to_selec:hasEquip()
			end
		end
	end,
	feasible = function(self,targets)
		if self:subcardsLength()>0 then
			return self:subcardsLength()==#targets
		else
			return #targets>0
		end
	end,
	about_to_use = function(self,room,use)
		use.from:setTag("kelqlizhongUse",ToData(use))
		self:cardOnUse(room,use)
	end,
	on_use = function(self,room,source,targets)
		local use = source:getTag("kelqlizhongUse"):toCardUse()
		for i,to in sgs.list(use.to)do
			if self:subcardsLength()>0 then
				room:setPlayerMark(source,"kelqlizhong1",1)
				local id = self:getSubcards():at(i)
				local c = sgs.Sanguosha:getCard(id)
				id = c:getRealCard():toEquipCard():location()
				if to:isAlive() and to:hasEquipArea(id) then
					room:moveCardTo(c,to,sgs.Player_PlaceEquip)
				end
			else
				room:setPlayerMark(source,"kelqlizhong1",2)
				to:drawCards(1,self:getSkillName())
				room:addMaxCards(to,2,false)
				room:attachSkillToPlayer(to,"kelqlizhongUse")
			end
		end
	end,
}
kelqlizhongvs = sgs.CreateViewAsSkill{
	name = "kelqlizhong",
	n = 998,
	view_filter = function(self,selected,to_select)
		return to_select:isKindOf("EquipCard")
		and sgs.Self:getMark("kelqlizhong1")~=1
	end,
	view_as = function(self,cards)
		if sgs.Self:getMark("kelqlizhong1")==2
		and #cards<1 then return end
		local new_card = kelqlizhongcard:clone()
		for _,c in ipairs(cards)do
			new_card:addSubcard(c)
		end
		return new_card
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@kelqlizhong"
	end,
	enabled_at_play = function(self,player)
		return false
	end,
}
kelqlizhong = sgs.CreateTriggerSkill{
	name = "kelqlizhong",
	view_as_skill = kelqlizhongvs,
	events = {sgs.EventPhaseStart,sgs.RoundEnd},
	can_trigger = function(self,target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Finish and player:hasSkill(self) then
			for i=1,2 do
				local pn = "kelqlizhong1"
				if i>1 then
					if player:getMark("kelqlizhong1")>0 then pn = "kelqlizhong3"
					else pn = "kelqlizhong2" end
				end
				if not room:askForUseCard(player,"@@kelqlizhong","kelqlizhong0:"..pn,-1,sgs.Card_MethodNone)
				then break end
			end
			room:setPlayerMark(player,"kelqlizhong1",0)
		elseif event == sgs.RoundEnd and player:hasSkill("kelqlizhongUse",true) then
			room:addMaxCards(player,-2,false)
			room:detachSkillFromPlayer(player,"kelqlizhongUse",true,true)
		end
	end,
}
kelqcaoren:addSkill(kelqlizhong)
kelqlizhongUse = sgs.CreateViewAsSkill{
	name = "kelqlizhongUse&",
	n = 1,
	view_filter = function(self,selected,to_select)
		return to_select:isEquipped()
	end,
	view_as = function(self,cards)
		if #cards<1 then return end
		local new_card = sgs.Sanguosha:cloneCard("nullification")
		new_card:setSkillName("_kelqlizhong")
		for _,c in ipairs(cards)do
			new_card:addSubcard(c)
		end
		return new_card
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern=="nullification"
	end,
	enabled_at_play = function(self,player)
		return false
	end,
}
if not sgs.Sanguosha:getSkill("kelqlizhongUse") then skills:append(kelqlizhongUse) end
kelqjuesui = sgs.CreateTriggerSkill{
	name = "kelqjuesui",
	events = {sgs.Dying}, 
	on_trigger = function(self,event,player,data,room)
		if event==sgs.Dying then
			local dying = data:toDying()
			if dying.who:getTag("kelqjuesuiUse"):toBool() then return end
			if player:askForSkillInvoke(self,ToData(dying.who)) then
				room:broadcastSkillInvoke(self:objectName())
				dying.who:setTag("kelqjuesuiUse",ToData(true))
				if dying.who:askForSkillInvoke(self,data,false) then
					room:recover(dying.who,sgs.RecoverStruct(self:objectName(),player,1-dying.who:getHp()))
					dying.who:throwEquipArea()
					room:attachSkillToPlayer(dying.who,"kelqjuesuiUse")
				end
			end
		end
		return false
	end
}
kelqcaoren:addSkill(kelqjuesui)
kelqjuesuiUse = sgs.CreateViewAsSkill{
	name = "kelqjuesuiUse&",
	n = 1,
	view_filter = function(self,selected,to_select)
		return to_select:getTypeId()>1 and to_select:isBlack()
	end,
	view_as = function(self,cards)
		if #cards<1 then return end
		local new_card = sgs.Sanguosha:cloneCard("slash")
		new_card:setSkillName("_kelqjuesui")
		for _,c in ipairs(cards)do
			new_card:addSubcard(c)
		end
		return new_card
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern=="slash"
	end,
	enabled_at_play = function(self,player)
		return sgs.Slash_IsAvailable(player)
	end,
}
if not sgs.Sanguosha:getSkill("kelqjuesuiUse") then skills:append(kelqjuesuiUse) end


sgs.LoadTranslationTable{

	["kelqcaoren"] = "曹仁[龙]", 
	["&kelqcaoren"] = "曹仁",
	["#kelqcaoren"] = "玉钤奉国",
	["designer:kelqcaoren"] = "官方",
	["cv:kelqcaoren"] = "官方",
	["illustrator:kelqcaoren"] = "鬼画府",

	["kelqlizhong"] = "厉众",
	[":kelqlizhong"] = "结束阶段，你可以将任意张装备牌置入任意名角色的装备区；你可以令你或任意名装备区里有牌的角色各摸一张牌，直到本轮结束，这些角色的手牌上限+2且可以将装备区里的牌当【无懈可击】使用。",
	["kelqlizhongUse"] = "厉众",
	[":kelqlizhongUse"] = "你的手牌上限+2且可以将装备区里的牌当【无懈可击】使用。",
	["kelqlizhong0"] = "厉众：你可以%src",
	["kelqlizhong1"] = "将任意张装备牌置入任意名角色的装备区；令你或任意名装备区里有牌的角色各摸一张牌",
	["kelqlizhong2"] = "将任意张装备牌置入任意名角色的装备区",
	["kelqlizhong3"] = "令你或任意名装备区里有牌的角色各摸一张牌",

	["kelqjuesui"] = "玦碎",
	[":kelqjuesui"] = "每名角色限一次，当一名角色进入濒死状态时，你可以令其选择是否回复体力至1点并废除装备区且其本局游戏可以将黑色非基本牌当无次数限制的【杀】使用。",
	["kelqjuesuiUse"] = "玦碎",
	[":kelqjuesuiUse"] = "你可以将黑色非基本牌当无次数限制的【杀】使用",

	["$kelqlizhong1"] = "倚我铁桶阵，敌军何以攻城？",
	["$kelqlizhong2"] = "严加防守，固若金汤，方处不败之地。",
	["$kelqjuesui1"] = "正是杀伐决断之时，将士们前进！",
	["$kelqjuesui2"] = "大敌当前，唯有死战，方能突围破敌！",

	["~kelqcaoren"] = "我誓与此城共存亡。",
}


kelqlvchang = sgs.General(extension, "kelqlvchang", "wei", 3)

kelqjuwu = sgs.CreateTriggerSkill{
	name = "kelqjuwu",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TargetConfirmed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetConfirmed) then
			local use = data:toCardUse()
			if use.card:objectName() == "slash" and use.to:contains(player) then
				local num = 0
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if use.from:inMyAttackRange(p) then
						num = num + 1
					end
				end
				if (num >= 3) then
					room:sendCompulsoryTriggerLog(player,self:objectName())
					local nullified_list = use.nullified_list
					table.insert(nullified_list, player:objectName())
					use.nullified_list = nullified_list
					data:setValue(use)
				end
			end
		end
	end,
}
kelqlvchang:addSkill(kelqjuwu)

kelqshouxiangcard = sgs.CreateSkillCard{
	name = "kelqshouxiangcard",
	will_throw = false,
	filter = function(self,targets,to_selec,source)
		return to_selec~=source and #targets<source:getMark("kelqshouxiangNum")
	end,
	feasible = function(self,targets)
		return self:subcardsLength()==#targets
	end,
	about_to_use = function(self,room,use)
		use.from:setTag("kelqshouxiangUse",ToData(use))
		self:cardOnUse(room,use)
	end,
	on_use = function(self,room,source,targets)
		local moves = sgs.CardsMoveList()
		local use = source:getTag("kelqshouxiangUse"):toCardUse()
		for i,to in sgs.list(use.to)do
			if to:isAlive() then
				local move = sgs.CardsMoveStruct(self:getSubcards():at(i),to,sgs.Player_PlaceHand,
				sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PREVIEW,source:objectName(),to:objectName(),self:getSkillName(),""))
				moves:append(move)
			end
		end
		room:moveCardsAtomic(moves,false)
	end,
}
kelqshouxiangvs = sgs.CreateViewAsSkill{
	name = "kelqshouxiang",
	n = 998,
	view_filter = function(self,selected,to_select)
		return #selected<sgs.Self:getMark("kelqshouxiangNum")
		and not to_select:isEquipped()
	end,
	view_as = function(self,cards)
		if #cards<1 then return end
		local new_card = kelqshouxiangcard:clone()
		for _,c in ipairs(cards)do
			new_card:addSubcard(c)
		end
		return new_card
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@kelqshouxiang"
	end,
	enabled_at_play = function(self,player)
		return false
	end,
}

kelqshouxiang = sgs.CreateTriggerSkill{
	name = "kelqshouxiang",
	view_as_skill = kelqshouxiangvs,
	events = {sgs.DrawNCards,sgs.EventPhaseChanging,sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DrawNCards) then
			local draw = data:toDraw()
			if draw.reason ~= "draw_phase" then return false end
			if player:askForSkillInvoke(self:objectName(), data) then
				local numt = 0
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:inMyAttackRange(player) then
						numt = numt + 1
					end
				end
				draw.num = draw.num + math.min(3,numt)
				data:setValue(draw)
				room:setPlayerMark(player,"kelqshouxiangskip-Clear",1)
			end
		elseif (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if player:getMark("kelqshouxiangskip-Clear")>0
			and change.to == sgs.Player_Play and not player:isSkipped(sgs.Player_Play) then
				player:skip(sgs.Player_Play)
			end
		elseif (event == sgs.EventPhaseStart) then
			if (player:getPhase() == sgs.Player_Discard)
			and (player:getMark("kelqshouxiangskip-Clear") > 0) then
				local numt = 0
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:inMyAttackRange(player) then
						numt = numt + 1
					end
				end
				if numt<1 then return end
				numt = math.min(3,numt)
				room:setPlayerMark(player,"kelqshouxiangNum",numt)
				room:askForUseCard(player,"@@kelqshouxiang","kelqshouxiang0:"..numt,-1,sgs.Card_MethodNone)
			end
		end
	end,
}
kelqlvchang:addSkill(kelqshouxiang)

sgs.LoadTranslationTable{

	["kelqlvchang"] = "吕常", 
	["&kelqlvchang"] = "吕常",
	["#kelqlvchang"] = "险守襄阳",
	["designer:kelqlvchang"] = "官方",
	["cv:kelqlvchang"] = "官方",
	["illustrator:kelqlvchang"] = "威屹",

	["kelqjuwu"] = "拒武",
	[":kelqjuwu"] = "锁定技，当你成为普通【杀】的目标后，若使用者攻击范围内包含至少三名角色，则此【杀】对你无效。",

	["kelqshouxiang-ask"] = "你可以选择发动“守襄”交给牌的角色",
	["kelqshouxiang-give"] = "请选择交给其的牌",
	["kelqshouxiang"] = "守襄",
	[":kelqshouxiang"] = "摸牌阶段，你可以多摸X张牌，然后跳过本回合的出牌阶段，且本回合的弃牌阶段开始时，你可以交给至多X名角色各一张手牌（X为攻击范围内包含你的角色数且至多为3）。",
	["kelqshouxiang0"] = "守襄：你可以交给至多%src名角色各一张手牌",


	["$kelqjuwu1"] = "",
	["$kelqjuwu2"] = "",
	["$kelqshouxiang1"] = "",
	["$kelqshouxiang2"] = "",

	["~kelqlvchang"] = "",
}





sgs.Sanguosha:addSkills(skills)
return {extension}

