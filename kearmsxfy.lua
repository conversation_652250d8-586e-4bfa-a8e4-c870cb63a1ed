--==《新武将》==--
extension_li = sgs.Package("kearmsxfyli", sgs.Package_GeneralPack)
extension_zhen = sgs.Package("kearmsxfyzhen", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

function KeToData(self)
	local data = sgs.QVariant()
	if type(self)=="string"
	or type(self)=="boolean"
	or type(self)=="number"
	then data = sgs.QVariant(self)
	elseif self~=nil then data:setValue(self) end
	return data
end

--buff集中
kearmsxfyslashmore = sgs.CreateTargetModSkill{
	name = "kearmsxfyslashmore",
	pattern = ".",
	residue_func = function(self, from, card, to)
		local n = 0
		--[[if card:getSkillName()=="kelqjuesui" and from:hasSkill("kelqjuesuiUse") then
			n = n + 1000
		end]]
		return n
	end,
	extra_target_func = function(self, from, card)
		local n = 0
		--[[if from:hasSkill("kesxhuiji")
		and card:isKindOf("Slash") then
			n = n + 1000
		end]]
		return n
	end,
	distance_limit_func = function(self, from, card, to)
		local n = 0
		--[[if card:isKindOf("Slash") and card:getSkillName() == "_keshuaiyansha" then
			n = n + 1000
		end]]
		return n
	end
}
--if not sgs.Sanguosha:getSkill("kearmsxfyslashmore") then skills:append(kearmsxfyslashmore) end

sgs.LoadTranslationTable{
    ["kearmsxfyli"] = "四象封印·少阴[离]",
	["kearmsxfyzhen"] = "四象封印·少阴[震]",
}

kesxdengzhi = sgs.General(extension_li, "kesxdengzhi", "shu", 3)

kesxjimeng = sgs.CreateTriggerSkill{
	name = "kesxjimeng",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) 
		and (player:getCardCount() > 0)
		and (player:getPhase() == sgs.Player_Start) then
			local ids = player:handCards()
			for _, id in sgs.qlist(player:getEquipsId()) do
				ids:append(id)
			end
			local fri = room:askForYiji(player,ids,self:objectName(),false,false,true,-1,sgs.SPlayerList(),sgs.CardMoveReason(),"kesxjimeng_ask",true)
			if fri and fri:getCardCount()>0 and player:isAlive() then
				ids = fri:handCards()
				for _, id in sgs.qlist(fri:getEquipsId()) do
					ids:append(id)
				end
				local tos = sgs.SPlayerList()
				tos:append(player)
				room:askForYiji(fri,ids,self:objectName(),false,false,false,-1,tos,sgs.CardMoveReason(),"kesxjimeng_choose:"..player:objectName())
			end
		end	
	end,
	--[[can_trigger = function(self, player)
		return true
	end]]
}
kesxdengzhi:addSkill(kesxjimeng)

kesxhehe = sgs.CreateTriggerSkill{
	name = "kesxhehe",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseEnd) and (player:getPhase() == sgs.Player_Draw) then
			local players = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if (p:getHandcardNum() == player:getHandcardNum()) then
					players:append(p)
				end
			end
			local fris = room:askForPlayersChosen(player, players, self:objectName(), 0, 2, "kesxhehe-ask", true, true)
			if fris:length() > 0 then
			    for _, q in sgs.qlist(fris) do
					q:drawCards(1,self:objectName())
				end
			end
		end	
	end,
	--[[can_trigger = function(self, player)
		return true
	end]]
}
kesxdengzhi:addSkill(kesxhehe)


sgs.LoadTranslationTable{

	["kesxdengzhi"] = "邓芝[离]", 
	["&kesxdengzhi"] = "邓芝",
	["#kesxdengzhi"] = "绝境的外交家",
	["designer:kesxdengzhi"] = "官方",
	["cv:kesxdengzhi"] = "官方",
	["illustrator:kesxdengzhi"] = "凝聚永恒",

	["kesxjimeng"] = "急盟",
	["kesxjimeng_ask"] = "你可以发动“急盟”交给一名角色任意张牌",
	["kesxjimeng_choose"] = "急盟：请选择交给 %src 的牌",
	[":kesxjimeng"] = "准备阶段，你可以交给一名其他角色至少一张牌，然后其交给你至少一张牌。",

	["kesxhehe"] = "和合",
	["kesxhehe-ask"] = "你可以发动“和合”令至多两名角色各摸一张牌",
	[":kesxhehe"] = "摸牌阶段结束时，你可以令至多两名手牌数与你相同的其他角色各摸一张牌。",

	["$kesxjimeng1"] = "精诚协作，以御北虏。",
	["$kesxjimeng2"] = "两家携手，共抗时艰。",
	["$kesxhehe1"] = "清廉严谨，以身作则。",
	["$kesxhehe2"] = "赏罚明断，自我而始。",

	["~kesxdengzhi"] = "大王命世之英，何行此不智之举？",
}

kesxwenyang = sgs.General(extension_li, "kesxwenyang", "wei", 4)

kesxquedi = sgs.CreateOneCardViewAsSkill{
	name = "kesxquedi",
	response_or_use = true,
	view_filter = function(self, card)
		if not card:isKindOf("Slash") then return false end
		local juedou = sgs.Sanguosha:cloneCard("duel")
		juedou:addSubcard(card:getEffectiveId())
		juedou:setSkillName("kesxquedi")
		juedou:deleteLater()
		return juedou:isAvailable(sgs.Self)
	end,
	view_as = function(self, card)
		local juedou = sgs.Sanguosha:cloneCard("duel")
		juedou:addSubcard(card:getId())
		juedou:setSkillName("kesxquedi")
		return juedou
	end,
	enabled_at_play = function(self, player)
		local juedou = sgs.Sanguosha:cloneCard("duel")
		juedou:setSkillName("kesxquedi")
		juedou:deleteLater()
		return juedou:isAvailable(player)
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern:contains("duel")
		and sgs.Sanguosha:getCurrentCardUseReason()==sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
	end
}
kesxwenyang:addSkill(kesxquedi)

sgs.LoadTranslationTable{

	["kesxwenyang"] = "文鸯[离]", 
	["&kesxwenyang"] = "文鸯",
	["#kesxwenyang"] = "独骑破军",
	["designer:kesxwenyang"] = "官方",
	["cv:kesxwenyang"] = "官方",
	["illustrator:kesxwenyang"] = "鬼画府",

	["kesxquedi"] = "却敌",
	[":kesxquedi"] = "你可以将一张【杀】当【决斗】使用。",

	["$kesxquedi1"] = "哼，缘何退却？有胆来战！",
	["$kesxquedi2"] = "八千之众，尚不如我一人乎？",

	["~kesxwenyang"] = "得报父仇，我无憾矣。",
}

kesxchengpu = sgs.General(extension_li, "kesxchengpu", "wu", 4)

kesxchunlao = sgs.CreateTriggerSkill{
    name = "kesxchunlao",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseEnd,sgs.CardsMoveOneTime},
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from and player:objectName() == move.from:objectName()
			and bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)==sgs.CardMoveReason_S_REASON_DISCARD
			and (player:getPhase() == sgs.Player_Discard) then
				local tag = player:getTag("kesxchunlaoToGet"):toIntList()
				for _,card_id in sgs.qlist(move.card_ids) do
					tag:append(card_id)
				end
				local d = sgs.QVariant()
				d:setValue(tag)
				player:setTag("kesxchunlaoToGet", d)
			end
		end
		if (event == sgs.EventPhaseEnd) then
			if (player:getPhase() == sgs.Player_Discard) then
				local tag = player:getTag("kesxchunlaoToGet"):toIntList()
				if (tag:length() >= 2 and player:hasSkill(self)) then
					local eny = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "kesxchunlao-ask",true,true)
					if eny then
						room:broadcastSkillInvoke(self:objectName())
						local dummy = sgs.Sanguosha:cloneCard("slash")
						dummy:addSubcards(eny:handCards())
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RECAST, eny:objectName(), self:objectName(), "")
						room:moveCardTo(dummy, nil, sgs.Player_DiscardPile, reason)
						dummy:deleteLater()
						dummy = sgs.Sanguosha:cloneCard("slash")
						for _,id in sgs.qlist(tag) do
							if room:getCardPlace(id)==sgs.Player_DiscardPile
							then dummy:addSubcard(id) end
						end
						eny:obtainCard(dummy)
						dummy:deleteLater()
						if eny:askForSkillInvoke(self:objectName(), KeToData("kesxchunlao0:"..player:objectName()), false) then
							room:recover(player, sgs.RecoverStruct())
						end
					end
				end
				player:removeTag("kesxchunlaoToGet")
			end
		end
	end
}
kesxchengpu:addSkill(kesxchunlao)

sgs.LoadTranslationTable{

	["kesxchengpu"] = "程普[离]", 
	["&kesxchengpu"] = "程普",
	["#kesxchengpu"] = "三朝虎臣",
	["designer:kesxchengpu"] = "官方",
	["cv:kesxchengpu"] = "官方",
	["illustrator:kesxchengpu"] = "玖等仁品",

	["kesxchunlao"] = "醇醪",
	["kesxchunlao:kesxchunlao0"] = "醇醪：你可以令 %src 回复1点体力",
	["kesxchunlao-ask"] = "你可以发动“醇醪”将弃置的牌交换一名其他角色的手牌",
	[":kesxchunlao"] = "弃牌阶段结束时，若你本阶段弃置了至少两张牌，你可以令一名其他角色将所有手牌置入弃牌堆并获得你弃置的牌，然后其可以令你回复1点体力。",

	["$kesxchunlao1"] = "醇酒佳酿杯中饮，醉酒提壶力千钧。",
	["$kesxchunlao2"] = "身被疮痍，唯酒能医。",

	["~kesxchengpu"] = "酒尽身死，壮哉！",
}

kesxlijue = sgs.General(extension_li, "kesxlijue", "qun", 5)

kesxxiongsuan = sgs.CreateTriggerSkill{
    name = "kesxxiongsuan",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and (player:getPhase() == sgs.Player_Start) then
			for _, p in sgs.qlist(room:getAllPlayers()) do 
				if (p:getHp() > player:getHp()) then
					return
				end
			end
			local players = sgs.SPlayerList()
			for _, q in sgs.qlist(room:getAllPlayers()) do 
				if q:getHp() == player:getHp() then
					players:append(q)
				end
			end
			room:sendCompulsoryTriggerLog(player, self)
			local fris = room:askForPlayersChosen(player, players, self:objectName(), 1, 99, "kesxxiongsuan-ask", true, true)
			for _, q in sgs.qlist(fris) do 
				room:damage(sgs.DamageStruct(self:objectName(), player, q))
			end
		end
	end
}
kesxlijue:addSkill(kesxxiongsuan)

sgs.LoadTranslationTable{

	["kesxlijue"] = "李傕[离]", 
	["&kesxlijue"] = "李傕",
	["#kesxlijue"] = "奸谋恶勇",
	["designer:kesxlijue"] = "官方",
	["cv:kesxlijue"] = "官方",
	["illustrator:kesxlijue"] = "凝聚永恒",

	["kesxxiongsuan"] = "兇算",
	["kesxxiongsuan-ask"] = "请选择发动“兇算”造成伤害的角色",
	[":kesxxiongsuan"] = "锁定技，准备阶段，若没有角色体力值大于你，你对至少一名体力值等于你的角色各造成1点伤害。",

	["$kesxxiongsuan1"] = "狼抗傲慢，祸福沿袭！",
	["$kesxxiongsuan2"] = "我就喜欢听这，狼嚎悲鸣！",

	["~kesxlijue"] = "这一次我拿不下长安了吗？",
}

kesxfeiyi = sgs.General(extension_li, "kesxfeiyi", "shu", 3)

kesxtiaoheCard = sgs.CreateSkillCard{
	name = "kesxtiaoheCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		if #targets == 0 then
			return to_select:getWeapon()
		end
		if #targets == 1 then
			return to_select:getArmor()
		end
		return #targets < 2
	end,
	feasible = function(self, targets)
		return #targets == 2
	end ,
	about_to_use = function(self,room,use)
		room:setTag("kesxtiaoheUse",ToData(use))
		self:cardOnUse(room,use)
	end,
	on_use = function(self, room, player, targets)
		local use = room:getTag("kesxtiaoheUse"):toCardUse()
		local w = use.to:at(0):getWeapon()
		if w and player:canDiscard(use.to:at(0),w:getEffectiveId()) then
			room:throwCard(w, self:getSkillName(), use.to:at(0), player)
		end
		local a = use.to:at(1):getArmor()
		if a and player:canDiscard(use.to:at(1),a:getEffectiveId()) then
			room:throwCard(a, self:getSkillName(), use.to:at(1), player)
		end
	end
}

kesxtiaohe = sgs.CreateViewAsSkill{
	name = "kesxtiaohe",
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return false
	end ,
	view_as = function(self, cards)
		return kesxtiaoheCard:clone()
	end ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#kesxtiaoheCard") 
	end
}
kesxfeiyi:addSkill(kesxtiaohe)

kesxqiansu = sgs.CreateTriggerSkill{
	name = "kesxqiansu",
	frequency = sgs.Skill_Frequent,
	events = {sgs.TargetConfirmed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if use.card:isKindOf("TrickCard") and use.to:contains(player) and (player:getEquipsId():length() == 0) then
				if room:askForSkillInvoke(player, self:objectName(), data) then 
					player:drawCards(1,self:objectName())
				end
			end
		end
	end,
}
kesxfeiyi:addSkill(kesxqiansu)

sgs.LoadTranslationTable{

	["kesxfeiyi"] = "费祎[离]", 
	["&kesxfeiyi"] = "费祎",
	["#kesxfeiyi"] = "洞世权相",
	["designer:kesxfeiyi"] = "官方",
	["cv:kesxfeiyi"] = "官方",
	["illustrator:kesxfeiyi"] = "凝聚永恒",

	["kesxtiaohe"] = "调和",
	[":kesxtiaohe"] = "出牌阶段限一次，你可以选择一名装备区有武器牌的角色和另一名装备区有防具牌的角色，然后你分别弃置这两张牌。",

	["kesxqiansu"] = "谦素",
	[":kesxqiansu"] = "当你成为锦囊牌的目标后，若你的装备区没有牌，你可以摸一张牌。",

	["$kesxtiaohe1"] = "斟酌损益，进尽忠言，此臣等之任也。",
	["$kesxtiaohe2"] = "两相匡护，以各安其分，兼尽其用。",
	["$kesxqiansu1"] = "承葛公遗托，富国安民。",
	["$kesxqiansu2"] = "保国治民，敬守社稷。",

	["~kesxfeiyi"] = "吾何惜一死，惜不见大汉中兴矣。",
}

kesxfanyufeng = sgs.General(extension_li, "kesxfanyufeng", "qun", 3,false)

kesxbazhanCard = sgs.CreateSkillCard{
	name = "kesxbazhanCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		return (#targets < 1) and to_select:isMale()
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:showCard(player,self:getSubcards():first())
		room:giveCard(player,target,self,self:getSkillName(),true)
		local cc = sgs.Sanguosha:getCard(self:getSubcards():first())
		local xxx = room:askForExchange(target, "kesxbazhan", 1, 1, false, "kesxbazhan-choose:"..player:objectName(), true, "^"..cc:getType()) 
		if xxx then
			room:showCard(target,xxx:getSubcards():first())
			room:giveCard(target,player,xxx,self:getSkillName(),true)
		end
	end
}

kesxbazhan = sgs.CreateViewAsSkill{
	name = "kesxbazhan",
	n = 1 ,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end ,
	view_as = function(self, cards)
		if #cards == 1 then
			local card = kesxbazhanCard:clone()
			card:addSubcard(cards[1])
			return card
		end
	end ,
	enabled_at_play = function(self, player)
		return player:usedTimes("#kesxbazhanCard") < 2
	end
}
kesxfanyufeng:addSkill(kesxbazhan)

kesxqiaoyingex = sgs.CreateCardLimitSkill{
	name = "#kesxqiaoyingex",
	limit_list = function(self, player)
		return "use"
	end,
	limit_pattern = function(self, player)
		if (player:getMark("&kesxqiaoying-Clear") < player:getHandcardNum())
		and (player:getMark("kesxqiaoyingeffect-Clear") > 0) then
			return ".|red|.|hand"
		end
		return ""
	end
}
kesxfanyufeng:addSkill(kesxqiaoyingex)

kesxqiaoying = sgs.CreateTriggerSkill{
	name = "kesxqiaoying",
	events = {sgs.EventPhaseStart,sgs.DamageInflicted},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if (event == sgs.DamageInflicted) then
			local damage = data:toDamage()
			if room:getCurrent():hasSkill("kesxqiaoying") 
			and (damage.to:getHandcardNum() > damage.to:getMark("&kesxqiaoying-Clear")) then
				room:sendCompulsoryTriggerLog(room:getCurrent(),self)
				damage.damage = 1 + damage.damage
				data:setValue(damage)
			end
		end
		if (event == sgs.EventPhaseStart) and player:hasSkill(self:objectName())
		and (player:getPhase() == sgs.Player_RoundStart) then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				room:setPlayerMark(p,"&kesxqiaoying-Clear",p:getHandcardNum())
				room:setPlayerMark(p,"kesxqiaoyingeffect-Clear",1)
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
}
kesxfanyufeng:addSkill(kesxqiaoying)
extension_li:insertRelatedSkills("kesxqiaoying", "#kesxqiaoyingex")

sgs.LoadTranslationTable{

	["kesxfanyufeng"] = "樊玉凤[离]", 
	["&kesxfanyufeng"] = "樊玉凤",
	["#kesxfanyufeng"] = "红鸾寡宿",
	["designer:kesxfanyufeng"] = "官方",
	["cv:kesxfanyufeng"] = "官方",
	["illustrator:kesxfanyufeng"] = "琬焱",

	["kesxbazhan"] = "把盏",
	["kesxbazhan-choose"] = "你可以交给 %src 一张牌",
	[":kesxbazhan"] = "出牌阶段限两次，你可以展示一张手牌并交给一名男性角色，然后其可以展示一张与此牌类别不同的手牌并交给你。",

	["kesxqiaoying"] = "醮影",
	[":kesxqiaoying"] = "在你的回合内，手牌数大于其当前回合开始时的手牌数的角色不能使用红色手牌且其受到的伤害+1。",

	["$kesxbazhan1"] = "今与将军把盏，酒不醉人人自醉。",
	["$kesxbazhan2"] = "昨日把盏消残酒，醉时朦胧见君来。",
	["$kesxqiaoying1"] = "经年相别，顾盼云泥，此间再未合影。",
	["$kesxqiaoying2"] = "举杯邀月盼云郎，我与月影成两人。",

	["~kesxfanyufeng"] = "浓酒只消昨日恨，奈何岁月败美人。 ",
}

kesxchengyu = sgs.General(extension_li, "kesxchengyu", "wei", 3,true)

kesxchengyu:addSkill("shefu")

kesxyibing = sgs.CreateTriggerSkill{
	name = "kesxyibing",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Dying},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Dying) then
			local dying_data = data:toDying()
			local source = dying_data.who
			if source~=player and source:getCardCount()>0
			and player:askForSkillInvoke(self, source) then
				room:broadcastSkillInvoke(self:objectName())
				local card_id = room:askForCardChosen(player, source, "he", self:objectName())
				local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
				room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
			end
		end
	end,
}
kesxchengyu:addSkill(kesxyibing)

sgs.LoadTranslationTable{

	["kesxchengyu"] = "程昱[离]", 
	["&kesxchengyu"] = "程昱",
	["#kesxchengyu"] = "泰山捧日",
	["designer:kesxchengyu"] = "官方",
	["cv:kesxchengyu"] = "官方",
	["illustrator:kesxchengyu"] = "DH",

	["kesxyibing"] = "益兵",
	[":kesxyibing"] = "当其他角色进入濒死状态时，你可以获得其一张牌。",

	["$kesxyibing1"] = "助曹公者昌，逆曹公者亡！",
	["$kesxyibing2"] = "愚民不可共济大事，必当与智者为伍。",

	["~kesxchengyu"] = "此诚报效国家之时，吾却休矣。",
}

kesxzhangyi = sgs.General(extension_li, "kesxzhangyi", "shu", 4,true)

kesxzhiyiVS = sgs.CreateViewAsSkill{
	name = "kesxzhiyi" ,
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return false
	end ,
	view_as = function(self, cards)
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("_kesxzhiyi")
		return slash
	end ,
	enabled_at_play = function()
		return false
	end ,
	enabled_at_response = function(self, player, pattern)
		return pattern=="@@kesxzhiyi"
	end
}
kesxzhiyi = sgs.CreateTriggerSkill{
	name = "kesxzhiyi" ,
	events = {sgs.EventPhaseChanging,sgs.CardUsed} ,
	view_as_skill = kesxzhiyiVS ,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				room:setPlayerMark(player,"&kesxzhiyi-Clear",1)
			end
		end
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) and (player:getMark("&kesxzhiyi-Clear") > 0) then
				room:sendCompulsoryTriggerLog(player, self:objectName())
				if sgs.Slash_IsAvailable(player)
				and room:askForUseCard(player, "@@kesxzhiyi", "kesxzhiyi-ask", 1)
				then return end
				player:drawCards(1,self:objectName())
			end
		end
	end
}
kesxzhangyi:addSkill(kesxzhiyi)

sgs.LoadTranslationTable{

	["kesxzhangyi"] = "张翼[离]", 
	["&kesxzhangyi"] = "张翼",
	["#kesxzhangyi"] = "亢锐怀忠",
	["designer:kesxzhangyi"] = "官方",
	["cv:kesxzhangyi"] = "官方",
	["illustrator:kesxzhangyi"] = "鬼画府",

	["kesxzhiyi"] = "执义",
	["kesxzhiyi:sha"] = "视为使用一张【杀】",
	["kesxzhiyi:draw"] = "摸一张牌",
	["kesxzhiyi-ask"] = "执义：你可以视为使用一张【杀】",
	[":kesxzhiyi"] = "锁定技，每个回合结束时，若你本回合使用过【杀】，你摸一张牌或视为使用一张【杀】。",

	["$kesxzhiyi1"] = "伯约勿扰，吾来助你！",
	["$kesxzhiyi2"] = "众将听令，此战可进不可退！",

	["~kesxzhangyi"] = "主公，季汉亡矣！",
}

kesxjianggan = sgs.General(extension_zhen, "kesxjianggan", "wei", 3, true)

kesxdaoshu = sgs.CreateTriggerSkill{
	name = "kesxdaoshu",
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start then
				for _, jg in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					if jg:isAlive() and jg:getMark("&usekesxdaoshu_lun")<1 then
						local canchooses = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getOtherPlayers(player)) do
							if not p:isKongcheng() then
								canchooses:append(p)
							end
						end
						local target = room:askForPlayerChosen(jg, canchooses, self:objectName(), "kesxdaoshu-ask", true, true)
						if target then
							room:broadcastSkillInvoke(self:objectName())
							room:setPlayerMark(jg,"&usekesxdaoshu_lun",1)
							local card_id = room:askForCardChosen(jg, target, "h", self:objectName())
							room:showCard(target,card_id)
							local thecard = sgs.Sanguosha:getCard(card_id)
							local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, jg:objectName())
							room:obtainCard(player, thecard, reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
							room:setPlayerMark(player, "&kesxdaoshu+:+"..thecard:getSuitString().."+-Clear",1)
							room:setPlayerMark(jg, "&kesxdaoshu+:+"..thecard:getSuitString().."+-Clear",1)
						end
					end
				end
			end
		end
	end,
	can_trigger = function(self,target)
		return target and target:isAlive()
	end,
}
kesxjianggan:addSkill(kesxdaoshu)

kesxdaoshuex = sgs.CreateCardLimitSkill{
	name = "#kesxdaoshuex",
	limit_list = function(self, player, card)
		return "use"
	end,
	limit_pattern = function(self, player, card)
		if (player:getMark("&kesxdaoshu+:+"..card:getSuitString().."+-Clear") > 0) then
			return ".|.|.|hand"
		end
		return ""
	end
}
kesxjianggan:addSkill(kesxdaoshuex)
--extension_zhen:insertRelatedSkills("kesxdaoshu", "#kesxdaoshuex")

kesxdaizui = sgs.CreateTriggerSkill{
	name = "kesxdaizui",
	events = {sgs.Damaged},
	frequency = sgs.Skill_Frequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Damaged) then
			if player:getMark("&usekesxdaoshu_lun") > 0 then
				room:sendCompulsoryTriggerLog(player,self)
				room:setPlayerMark(player,"&usekesxdaoshu_lun", 0)
			end
		end
	end
}
kesxjianggan:addSkill(kesxdaizui)

sgs.LoadTranslationTable{

	["kesxjianggan"] = "蒋干[震]", 
	["&kesxjianggan"] = "蒋干",
	["#kesxjianggan"] = "独步江淮",
	["designer:kesxjianggan"] = "官方",
	["cv:kesxjianggan"] = "官方",
	["illustrator:kesxjianggan"] = "官方",

	["kesxdaoshu"] = "盗书",
	["usekesxdaoshu"] = "已盗书",
	["kesxdaoshu-ask"] = "你可以选择发动“盗书”的角色",
	[":kesxdaoshu"] = "每轮限一次，一名角色的准备阶段，你可以展示另一名角色的一张手牌并令其获得之，然后你与其本回合不能使用与该牌花色相同的手牌。",

	["kesxdaizui"] = "戴罪",
	[":kesxdaizui"] = "当你受到伤害后，你本轮视为未发动过“盗书”。",

	["$kesxdaoshu1"] = "在此机要之地，何不一窥东吴军机。",
	["$kesxdaoshu2"] = "哦？密信……果然有所收获。",
	["$kesxdaizui1"] = "望丞相权且记过，容干将功折罪啊！",
	["$kesxdaizui2"] = "干，谢丞相不杀之恩！",

	["~kesxjianggan"] = "唉！假信害我不浅啊……",
}

kesxmayunlu = sgs.General(extension_zhen, "kesxmayunlu", "shu", 4, false)

kesxfenghun = sgs.CreateTriggerSkill{
	name = "kesxfenghun",
	events = {sgs.DamageCaused},
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageCaused) then
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("Slash") then
				local use = room:getUseStruct(damage.card)
				if not use.to:contains(damage.to) then return end
				local tos = sgs.SPlayerList()
				if player:canDiscard(player, "he") then 
					tos:append(player)
				end
				if player:canDiscard(damage.to, "he") then
					tos:append(damage.to)
				end
				local to = room:askForPlayerChosen(player, tos, self:objectName(), "kesxfenghun-ask", true, true)
				if to then
					room:broadcastSkillInvoke(self:objectName())
					local to_throw = room:askForCardChosen(player, to, "he", self:objectName())
					local card = sgs.Sanguosha:getCard(to_throw)
					room:throwCard(card, to, player)
					if (card:getSuit() == sgs.Card_Diamond) then
						damage.damage = 1 + damage.damage
						data:setValue(damage)
					end
				end
			end
		end
	end
}
kesxmayunlu:addSkill(kesxfenghun)

kesxmayunlu:addSkill("mashu")

sgs.LoadTranslationTable{

	["kesxmayunlu"] = "马云騄[震]", 
	["&kesxmayunlu"] = "马云騄",
	["#kesxmayunlu"] = "剑胆琴心",
	["designer:kesxmayunlu"] = "官方",
	["cv:kesxmayunlu"] = "官方",
	["illustrator:kesxmayunlu"] = "叶碧芳",

	["kesxfenghun"] = "凤魂",
	["kesxfenghun-ask"] = "你可以发动“凤魂”弃置你或目标一张牌",
	[":kesxfenghun"] = "当你使用【杀】对目标角色造成伤害时，你可以弃置你或其一张牌，若此牌为♦，此伤害+1。",


	["$kesxfenghun1"] = "贼人是不是被本姑娘给吓破胆了呀？",
	["$kesxfenghun2"] = "看我不好好杀杀你的威风！",

	["~kesxmayunlu"] = "子龙哥哥，救我~",
}

kesxmateng = sgs.General(extension_zhen, "kesxmateng$", "qun", 4, true)

kesxxiongyiCard = sgs.CreateSkillCard{
	name = "kesxxiongyiCard",
	will_throw = false,
	filter = function(self, selected, to_select)
		return (#selected < 99)
	end,
	on_use = function(self, room, source, targets)
		room:removePlayerMark(source,"@kesxxiongyi")
		while #targets>0 do
			for _, p in ipairs(targets) do
				local cantargets = sgs.SPlayerList()
				for _, pp in sgs.qlist(room:getAllPlayers()) do
					if p:canSlash(pp,false) then cantargets:append(pp) end
				end
				if not room:askForUseSlashTo(p, cantargets,"kesxxiongyi-ask",true,false,false,nil,nil,"kesxxiongyiflag") then
					return
				end
			end
		end
	end,
}
kesxxiongyiVS = sgs.CreateZeroCardViewAsSkill{
	name = "kesxxiongyi",
	view_as = function(self, cards)
		return kesxxiongyiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@kesxxiongyi") >= 1
	end
}

kesxxiongyi = sgs.CreateTriggerSkill{
	name = "kesxxiongyi",
	view_as_skill = kesxxiongyiVS,
	events = {sgs.TargetConfirmed,sgs.TargetSpecified} ,
	frequency = sgs.Skill_Limited,
	limit_mark = "@kesxxiongyi",
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetSpecified) then	
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") and use.card:hasFlag("kesxxiongyiflag") then
				local log = sgs.LogMessage()
				log.type = "$kesxxiongyilog"
				log.from = player
				room:sendLog(log)
				local no_respond_list = use.no_respond_list
				for _, szm in sgs.qlist(use.to) do
					table.insert(no_respond_list, szm:objectName())
				end
				use.no_respond_list = no_respond_list
				data:setValue(use)	
			end
		end
	end
}
kesxmateng:addSkill(kesxxiongyi)

kesxmateng:addSkill("mashu")
kesxyouqiCard = sgs.CreateSkillCard{
	name = "kesxyouqiCard",
	--target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		if #targets<1 and to_select:getKingdom() == "qun" then
			return to_select:getOffensiveHorse()~=nil or to_select:getDefensiveHorse()~=nil
		elseif #targets==1 then
			return targets[1]:getOffensiveHorse() and not to_select:getOffensiveHorse() and to_select:hasOffensiveHorseArea()
			or targets[1]:getDefensiveHorse() and not to_select:getDefensiveHorse() and to_select:hasDefensiveHorseArea()
		end
	end,
	feasible = function(self,targets)
		return #targets==2
	end,
	about_to_use = function(self,room,use)
		room:setTag("kesxyouqiUse",ToData(use))
		self:cardOnUse(room,use)
	end,
	on_use = function(self,room,source,targets)
	   	local use = room:getTag("kesxyouqiUse"):toCardUse()
		local to1 = use.to:at(0)
	   	local to2 = use.to:at(1)
		local ids = sgs.IntList()
		for _,e in sgs.list(to1:getEquips())do
			if e:isKindOf("Horse") then
				local n = e:getRealCard():toEquipCard():location()
				if to2:hasEquipArea(n) and not to2:getEquip(n) then
					continue
				end
			end
			ids:append(e:getEffectiveId())
		end
		local id = room:askForCardChosen(source,to1,"e","kesxyouqi",false,sgs.Card_MethodNone,ids)
		if id>-1 and to2:isAlive() then
			room:moveCardTo(sgs.Sanguosha:getCard(id),to2,sgs.Player_PlaceEquip)
		end
	end
}

kesxyouqivs = sgs.CreateViewAsSkill{
	name = "kesxyouqi",
	n = 0;
	view_as = function(self, cards)
		return kesxyouqiCard:clone()
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern=="@@kesxyouqi"
	end,
	enabled_at_play = function(self, player)
		return false
	end
}

kesxyouqi = sgs.CreateTriggerSkill{
	name = "kesxyouqi$",
	events = {sgs.EventPhaseStart},
	view_as_skill = kesxyouqivs,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and player:hasLordSkill(self:objectName())
		and (player:getPhase() == sgs.Player_Start) then
			local choosetargets = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:getKingdom() == "qun" and (p:getOffensiveHorse() or p:getDefensiveHorse()) then
				    --检查能否移到其他角色的区域内
					for _, pto in sgs.qlist(room:getOtherPlayers(p)) do
						if (p:getOffensiveHorse() and pto:hasOffensiveHorseArea() and not pto:getOffensiveHorse())
						or (p:getDefensiveHorse() and pto:hasDefensiveHorseArea() and not pto:getDefensiveHorse()) then
							room:askForUseCard(player, "@@kesxyouqi", "@kesxyouqi",-1,sgs.Card_MethodNone)
							return
						end
					end
				end
			end
		end
	end
}
kesxmateng:addSkill(kesxyouqi)

sgs.LoadTranslationTable{

	["kesxmateng"] = "马腾[震]", 
	["&kesxmateng"] = "马腾",
	["#kesxmateng"] = "勇冠西州",
	["designer:kesxmateng"] = "官方",
	["cv:kesxmateng"] = "官方",
	["illustrator:kesxmateng"] = "峰雨同程",

	["kesxxiongyi"] = "雄异",
	["$kesxxiongyilog"] = "%from 的“<font color='yellow'><b>雄异</b></font>”生效，此【杀】不能被响应 ",
	["kesxxiongyi-ask"] = "雄异：你可以使用一张【杀】",
	[":kesxxiongyi"] = "限定技，出牌阶段，你可以令任意名角色依次选择是否使用一张【杀】且此【杀】不能被响应，然后这些角色重复此流程，直到其中一名角色选择否。",

	["kesxyouqi"] = "游骑",
	["@kesxyouqi"] = "你可以发动“游骑”移动一名角色的坐骑牌",
	[":kesxyouqi"] = "主公技，准备阶段，你可以将一名群势力角色装备区内的一张坐骑牌移动到另一名角色的装备区。",


	["$kesxxiongyi1"] = "集众人之力，成群雄霸业！",
	["$kesxxiongyi2"] = "将士们，随我起事！",

	["~kesxmateng"] = "逆子无谋，祸及全族。",
}

kesxsunhao = sgs.General(extension_zhen, "kesxsunhao$", "wu", 5, true)

kesxcanshi = sgs.CreateTriggerSkill{
	name = "kesxcanshi",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawNCards,sgs.TargetSpecifying},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DrawNCards then
			local draw = data:toDraw()
			if not (draw.reason == "draw_phase") then return false end
			room:sendCompulsoryTriggerLog(player,self)
			local nnn = 0
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:isWounded() then
					nnn = nnn + 1
				end
			end
			draw.num = math.max(1,nnn)
			data:setValue(draw)
			room:setPlayerMark(player,"&kesxcanshi-Clear",1)
		elseif (event == sgs.TargetSpecifying) and (player:getMark("&kesxcanshi-Clear") > 0) then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") or use.card:isNDTrick() then
				local tri = 0
				for _, p in sgs.qlist(use.to) do
					if p:isWounded() then
						tri = 1
						break
					end
				end
				if (tri == 1) and player:canDiscard(player, "he") then
					room:askForDiscard(player, self:objectName(), 1, 1, false, true, "kesxcanshi-ask") 
				end
			end
		end
	end,
}
kesxsunhao:addSkill(kesxcanshi)

kesxsunhao:addSkill("chouhai")
kesxsunhao:addSkill("guiming")

sgs.LoadTranslationTable{

	["kesxsunhao"] = "孙皓[震]", 
	["&kesxsunhao"] = "孙皓",
	["#kesxsunhao"] = "时日曷丧",
	["designer:kesxsunhao"] = "官方",
	["cv:kesxsunhao"] = "官方",
	["illustrator:kesxsunhao"] = "LiuHeng",

	["kesxcanshi"] = "残蚀",
	["kesxcanshi-ask"] = "残蚀：请弃置一张牌",
	[":kesxcanshi"] = "锁定技，摸牌阶段，你令摸牌数改为已受伤角色数且至少为1，然后你本回合使用【杀】或普通锦囊牌指定已受伤角色为目标时，你弃置一张牌。",

	["$kesxcanshi1"] = "众人与蝼蚁何异？哼哼哼...",
	["$kesxcanshi2"] = "难道一切不在朕手中？",

	["~kesxsunhao"] = "命啊，命！",
}

kesxluotong = sgs.General(extension_zhen, "kesxluotong", "wu", 3, true)

kesxjinjian = sgs.CreateTriggerSkill{
	name = "kesxjinjian",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageCaused,sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageCaused) then
			local damage = data:toDamage()
			if (player:getMark("&kesxjinjianadd-Clear") > 0) then
				room:sendCompulsoryTriggerLog(player,self)
				damage.damage = 1 + damage.damage
				data:setValue(damage)
				room:setPlayerMark(player,"&kesxjinjianadd-Clear",0)
			elseif player:getMark("kesxjinjianhit-Clear") == 0 then
				if player:askForSkillInvoke(self:objectName(), KeToData("kesxjinjian0:"..damage.to:objectName())) then
					room:broadcastSkillInvoke(self:objectName())
					room:setPlayerMark(player,"kesxjinjianhit-Clear",1)
					room:setPlayerMark(player,"&kesxjinjianadd-Clear",1)
					return true
				end
			end
		end
		if (event == sgs.DamageInflicted) then
			local damage = data:toDamage()
			if (player:getMark("&kesxjinjianmin-Clear") > 0) then
				room:sendCompulsoryTriggerLog(player,self)
				damage.damage = 1 + damage.damage
				data:setValue(damage)
				room:setPlayerMark(player,"&kesxjinjianmin-Clear",0)
			elseif (player:getMark("kesxjinjianbehit-Clear") == 0) then
				if player:askForSkillInvoke(self:objectName(), KeToData("kesxjinjian1:"..damage.from:objectName())) then
					room:broadcastSkillInvoke(self:objectName())
					room:setPlayerMark(player,"kesxjinjianbehit-Clear",1)
					room:setPlayerMark(player,"&kesxjinjianmin-Clear",1)
					return true
				end
			end
		end
	end,
}
kesxluotong:addSkill(kesxjinjian)

kesxrenzheng = sgs.CreateTriggerSkill{
	name = "kesxrenzheng",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageComplete},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageComplete) then
			local damage = data:toDamage()
			if damage.prevented then
				for _, sh in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					room:sendCompulsoryTriggerLog(sh,self)
					room:getCurrent():drawCards(1,self:objectName())
				end
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end
}
kesxluotong:addSkill(kesxrenzheng)

sgs.LoadTranslationTable{

	["kesxluotong"] = "骆统[震]", 
	["&kesxluotong"] = "骆统",
	["#kesxluotong"] = "蹇谔匪躬",
	["designer:kesxluotong"] = "官方",
	["cv:kesxluotong"] = "官方",
	["illustrator:kesxluotong"] = "第七个桔子",

	["kesxjinjian"] = "进谏",
	["kesxjinjian:kesxjinjian0"] = "你将对 %src 造成伤害，你可以发动“进谏”防止此伤害",
	["kesxjinjian:kesxjinjian1"] = "%src 将对你造成伤害，你可以发动“进谏”防止此伤害",
	["kesxjinjianadd"] = "进谏加伤",
	["kesxjinjianmin"] = "进谏减伤",
	[":kesxjinjian"] = "每回合各限一次，当你受到/造成伤害时，你可以防止此伤害，然后你本回合下次受到伤害/造成伤害时，此伤害+1。",

	["kesxrenzheng"] = "仁政",
	[":kesxrenzheng"] = "锁定技，每次伤害结算后，若此伤害已被防止，你令当前回合角色摸一张牌。",

	["$kesxjinjian1"] = "臣有一言，藏之如鲠在喉，今不吐不快！",
	["$kesxjinjian2"] = "胥吏者，百姓之所倚、天子之所期，焉能哑然？",
	["$kesxrenzheng1"] = "兴亡百姓皆苦，统之所愿者，苦尽而甘来也。",
	["$kesxrenzheng2"] = "政之施者当可克仁而为之，如此方成大同。",

	["~kesxluotong"] = "上愧天子，下愧百姓，焉能苟活？ ",
}

kesxyanghu = sgs.General(extension_zhen, "kesxyanghu", "wei", 4, true)

kesxmingfaCard = sgs.CreateSkillCard{
	name = "kesxmingfaCard",
	filter = function(self, targets, to_select)
		return (#targets < 1) and (to_select:getHp() > 1)
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:damage(sgs.DamageStruct("kesxmingfa", player, target))
		room:setPlayerMark(player,"&usekesxmingfa",1)
		room:setPlayerMark(player,"usekesxmingfa"..target:objectName(),1)
		room:setPlayerMark(target,"&kesxmingfa",1)
	end
}
--主技能
kesxmingfaVS = sgs.CreateViewAsSkill{
	name = "kesxmingfa",
	n = 0,
	view_as = function(self, cards)
		return kesxmingfaCard:clone()
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("&usekesxmingfa") == 0)
	end, 
}

kesxmingfa = sgs.CreateTriggerSkill{
	name = "kesxmingfa",
	view_as_skill = kesxmingfaVS,
	events = {sgs.Death,sgs.HpRecover},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.HpRecover) then
			local rec = data:toRecover()
			if (rec.who:getMark("&kesxmingfa") > 0) then
				room:setPlayerMark(rec.who,"&kesxmingfa",0)
				for _, yh in sgs.qlist(room:getAllPlayers()) do
					if yh:getMark("usekesxmingfa"..rec.who:objectName()) > 0 then
					    room:setPlayerMark(yh,"usekesxmingfa"..rec.who:objectName(),0)
					    room:setPlayerMark(yh,"&usekesxmingfa",0)
					end
				end
			end
		end
		if (event == sgs.Death) then
			local death = data:toDeath()
			if (death.who:getMark("&kesxmingfa") > 0) then
				room:setPlayerMark(death.who,"&kesxmingfa",0)
				for _, yh in sgs.qlist(room:getAllPlayers()) do
					if yh:getMark("usekesxmingfa"..death.who:objectName()) > 0 then
					    room:setPlayerMark(yh,"usekesxmingfa"..death.who:objectName(),0)
					    room:setPlayerMark(yh,"&usekesxmingfa",0)
					end
				end
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end
}
kesxyanghu:addSkill(kesxmingfa)

sgs.LoadTranslationTable{

	["kesxyanghu"] = "羊祜[震]", 
	["&kesxyanghu"] = "羊祜",
	["#kesxyanghu"] = "制纮同轨",
	["designer:kesxyanghu"] = "官方",
	["cv:kesxyanghu"] = "官方",
	["illustrator:kesxyanghu"] = "芝芝不加糖",

	["kesxmingfa"] = "明伐",
	["usekesxmingfa"] = "明伐失效",
	[":kesxmingfa"] = "出牌阶段，你可以对一名体力值大于1的角色造成1点伤害，然后本技能失效直到其死亡或回复体力。",

	["$kesxmingfa1"] = "以诚相待，吴人倾心，攻之必克。",
	["$kesxmingfa2"] = "以强击弱，易如反掌，何须诡诈？",

	["~kesxyanghu"] = "憾东吴尚存，天下未定也。",
}

kesxlvlingqi = sgs.General(extension_zhen, "kesxlvlingqi", "qun", 4, false)

kesxhuiji = sgs.CreateTriggerSkill{
	name = "kesxhuiji",
	events = {sgs.TargetSpecifying, sgs.CardAsked,sgs.CardFinished,sgs.CardEffected},
	frequency = sgs.Skill_Frequent, 
	can_trigger = function(self,target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		--清除
		if (event == sgs.CardFinished) then
			local use = data:toCardUse()
			if use.card:hasFlag("kesxhuijiflag") then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					room:setPlayerMark(p,"&kesxhuiji-Clear",0)
				end
			end
		end
		if (event == sgs.CardEffected) then
			local effect = data:toCardEffect()
			if effect.card:hasFlag("kesxhuijiflag") then
				player:setFlags("kesxhuijiAsked")
			end
		end
		if (event == sgs.CardAsked) and (player:hasFlag("kesxhuijiAsked")) then
			local pattern = data:toStringList()
			if pattern[1]~="jink" or pattern[2]=="kesxhuiji-help"
			or pattern[3]~="use" then return false end
			player:setFlags("-kesxhuijiAsked")
			local lieges = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if p:getMark("&kesxhuiji-Clear") > 0 then
					lieges:append(p)
				end
			end
			if lieges:length()<1 or not player:askForSkillInvoke(self,data,false) then return false end
			for _,fri in sgs.qlist(lieges)do
				local jink = room:askForUseCard(fri, "jink", "kesxhuiji-help", -1, sgs.Card_MethodUse, false, player)
				if jink then
					room:provide(jink)
					return true
				end
			end
			return false
		end
		--尿分叉
		if (event == sgs.TargetSpecifying) and player:hasSkill(self:objectName()) then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				local extargets = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if (not use.to:contains(p)) and player:canSlash(p, use.card, true) then 
						extargets:append(p)
					end
				end
				player:setTag("kesxhuijiUse",data)
				local enys = room:askForPlayersChosen(player, extargets, self:objectName(), 0, 2, "kesxhuiji-ask", true, false)
				if enys:length() > 0 then
					room:broadcastSkillInvoke(self:objectName())
					room:setCardFlag(use.card,"kesxhuijiflag")
					for _,q in sgs.qlist(enys) do
						use.to:append(q)
					end
					room:sortByActionOrder(use.to)
					data:setValue(use)
					for _,qq in sgs.qlist(use.to) do
						room:setPlayerMark(qq,"&kesxhuiji-Clear",1)
					end
				end
			end
		end
	end
}
kesxlvlingqi:addSkill(kesxhuiji)

sgs.LoadTranslationTable{

	["kesxlvlingqi"] = "吕玲绮[震]", 
	["&kesxlvlingqi"] = "吕玲绮",
	["#kesxlvlingqi"] = "无双虓姬",
	["designer:kesxlvlingqi"] = "官方",
	["cv:kesxlvlingqi"] = "官方",
	["illustrator:kesxlvlingqi"] = "匠人绘",

	["kesxhuiji"] = "挥戟",
	[":kesxhuiji"] = "当你使用【杀】指定目标时，你可以令至多两名角色成为此【杀】的额外目标，然后当此【杀】的目标需要响应此【杀】时，其可以令其余目标选择是否代替其使用【闪】。",
	["kesxhuiji-ask"] = "挥戟：你可以为此【杀】额外指定两名目标",

	["$kesxhuiji1"] = "虓女暴怒发冲冠，画戟刃过惊雷断！",
	["$kesxhuiji2"] = "纵马执戟冲敌阵，天下谁人敢当锋！",

	["~kesxlvlingqi"] = "戟断马亡，此地竟是我的葬身之处吗？",
}


kesxzhouchu = sgs.General(extension_zhen, "kesxzhouchu", "wu", 4, true)

kesxxiongxiaCard = sgs.CreateSkillCard{
	name = "kesxxiongxiaCard",
	target_fixed = false,
	will_throw = false,
	mute = true,
	filter = function(self, targets, to_select, source)
		local duel = sgs.Sanguosha:cloneCard("duel")
		duel:addSubcards(self:getSubcards())
		duel:setSkillName("kesxxiongxia")
		duel:deleteLater()
		return #targets < 2 and duel:targetFilter(sgs.PlayerList(),to_select,source)
	end,
	feasible = function(self, targets)
		return #targets == 2
	end ,
	about_to_use = function(self,room,use)
		local duel = sgs.Sanguosha:cloneCard("duel")
		duel:addSubcards(self:getSubcards())
		duel:setSkillName("kesxxiongxia")
		use.card = duel
		room:useCard(use, true)
		duel:deleteLater()
	end
}

kesxxiongxiaVS = sgs.CreateViewAsSkill{
	name = "kesxxiongxia",
	n = 2,
	view_filter = function(self, selected, to_select)
        return true
	end,
	view_as = function(self, cards)
		if #cards > 1 then
			local duel = sgs.Sanguosha:cloneCard("duel")
			for _,c in ipairs(cards) do
				duel:addSubcard(c)
			end
			duel:setSkillName("kesxxiongxia")
			duel:deleteLater()
			if sgs.Self:isLocked(duel) then return end
			local duel = kesxxiongxiaCard:clone()
			for _,c in ipairs(cards) do
				duel:addSubcard(c)
			end
			return duel
		end
	end,
	enabled_at_play = function(self, player)
		return player:getMark("&bankesxxiongxia-Clear")<1
		and player:getCardCount()>1
	end, 
}

kesxxiongxia = sgs.CreateTriggerSkill{
	name = "kesxxiongxia",
	view_as_skill = kesxxiongxiaVS,
	events = {sgs.CardFinished},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardFinished) then
			local use = data:toCardUse()
			if (use.card:getSkillName() == "kesxxiongxia") then
				for _, q in sgs.qlist(use.to) do
					if not use.card:hasFlag("DamageDone_"..q:objectName()) then
						return
					end
				end
				room:setPlayerMark(player,"&bankesxxiongxia-Clear",1)
			end
		end
	end,
}
kesxzhouchu:addSkill(kesxxiongxia)

sgs.LoadTranslationTable{

	["kesxzhouchu"] = "周处[震]", 
	["&kesxzhouchu"] = "周处",
	["#kesxzhouchu"] = "英情天逸",
	["designer:kesxzhouchu"] = "官方",
	["cv:kesxzhouchu"] = "官方",
	["illustrator:kesxzhouchu"] = "MUMU",

	["kesxxiongxia"] = "兇侠",
	["bankesxxiongxia"] = "兇侠失效",
	[":kesxxiongxia"] = "出牌阶段，你可以将两张牌当【决斗】对两名其他角色使用，此牌结算后，若此牌对所有目标角色均造成过伤害，本技能失效直到本回合结束。",

	["$kesxxiongxia1"] = "入林射猛虎，投水斩孽蛟！",
	["$kesxxiongxia2"] = "此害不除，焉除三害！",

	["~kesxzhouchu"] = "刹那遭罪，殃堕无间……",
}




sgs.Sanguosha:addSkills(skills)
return {extension_li, extension_zhen}

