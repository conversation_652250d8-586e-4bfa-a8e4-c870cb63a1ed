--==《符文系统》==--
extension = sgs.Package("kefuwensys", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

--双城之战标记
arcanemark = sgs.CreateTriggerSkill{
	name = "arcanemark",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.GameStart},
	on_trigger = function(self, event, player, data)
		if event == sgs.GameStart then
			local room = player:getRoom()
			if (player:hasSkill("azirshenji")) or (player:hasSkill("kejinx_nopay")) or (player:hasSkill("keblood"))
			or (player:hasSkill("keguangfu")) or (player:hasSkill("kejingwen")) or (player:hasSkill("kechupan"))
			or (player:hasSkill("ketinghu")) or (player:hasSkill("kezhengyi")) or (player:hasSkill("keyuji"))
			or (player:hasSkill("kejintuo")) or (player:hasSkill("ezzhouneng")) then
				room:setPlayerMark(player,"arcanerole-Clear",1)
			end
			if (player:getGeneralName() == "kegift") then 
				room:change<PERSON><PERSON>(player, "ol_caocao", true, false, false, false)
			end
			if (player:getGeneral2Name() == "kegift") then
				room:changeHero(player, "ol_caocao", true, false, true, false)
			end
		end
	end,
	priority = 5,
}
if not sgs.Sanguosha:getSkill("arcanemark") then skills:append(arcanemark) end



--开局选符文
kegiftsystem = sgs.CreateTriggerSkill{
	name = "kegiftsystem",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.GameStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		--如果是本扩展包人物，就可以选择天赋
		if (player:getState() == "online") then 
			if (player:getMark("arcanerole-Clear") > 0) or (not table.contains(sgs.Sanguosha:getBanPackages(), "kefuwensys")) then
				local result = room:askForChoice(player,"typeofgift","jingmi+zhuzai+wushu+jianjue+qidi+zhsskill+arcane+cancel")
				--精密
				if result == "jingmi" then
					local resultjm = room:askForChoice(player,"jingmigift","qianggong+zhimingjiezou+xunjiebufa+cancel")
					if resultjm == "qianggong" then
						room:attachSkillToPlayer(player,"keqianggong")
						room:broadcastSkillInvoke("keqianggong")
						room:addPlayerMark(player, "@keqianggong")
					end
					if resultjm == "zhimingjiezou" then
						room:attachSkillToPlayer(player,"kezhimingjiezou")
						room:broadcastSkillInvoke("kezhimingjiezou")
						room:addPlayerMark(player, "@kezhimingjiezou")
					end
					if resultjm == "xunjiebufa" then
						room:attachSkillToPlayer(player,"kexunjiebufa")
						room:broadcastSkillInvoke("kexunjiebufa")
						--room:attachSkillToPlayer(player,"kexunjiebufajuli")
					end
					if resultjm == "cancel" then
		
					end
				end
				--主宰
				if result == "zhuzai" then 
					local resultzz = room:askForChoice(player,"zhuzaigift","dianxing+darkshouge+wqls+cancel")
					if resultzz == "dianxing" then
						room:attachSkillToPlayer(player,"kedianxing")
						room:addPlayerMark(player, "@dianxing")
						room:broadcastSkillInvoke("kedianxing")
					end
					if resultzz == "wqls" then
						room:attachSkillToPlayer(player,"kewuqinglieshou")
					end
					if resultzz == "darkshouge" then
						room:attachSkillToPlayer(player,"kedarkshouge")
						room:broadcastSkillInvoke("kedarkshouge",1)
					end
					if resultzz == "cancel" then
		
					end
		
				end
				--巫术
				if result == "wushu" then 
					local resultws = room:askForChoice(player,"wushugift","falidai+cancel")
					if resultws == "falidai" then
						room:attachSkillToPlayer(player,"kefalidai")
						--room:attachSkillToPlayer(player,"kefalidaiKeep")
					end
					if resultws == "cancel" then
		
					end
		
				end
				--坚决
				if result == "jianjue" then 
					local resultjj = room:askForChoice(player,"jianjuegift","shouhuzhe+cancel")
					if resultjj == "shouhuzhe" then
						room:broadcastSkillInvoke("keshouhuzhe")
						room:attachSkillToPlayer(player,"keshouhuzhe")
					end
					if resultjj == "cancel" then
		
					end
		
				end
				--启迪
				if result == "qidi" then 
					local resultqd = room:askForChoice(player,"qidigift","stole+binggan+miaobiao+cancel")
					if resultqd == "stole" then
						room:attachSkillToPlayer(player,"kestole")
						room:addPlayerMark(player, "@kestole")
						room:broadcastSkillInvoke("kestole")
					end
					if resultqd == "binggan" then
						room:attachSkillToPlayer(player,"kebingganpeisong")
						room:addPlayerMark(player, "@binggan",4)
					end
					if resultqd == "miaobiao" then
						room:addPlayerMark(player, "@kemiaobiao")
						room:attachSkillToPlayer(player,"kejinshenend")
					end
					if resultqd == "cancel" then
		
					end
		
				end
				--召唤师技能
				if result == "zhsskill" then 
					local resultzh = room:askForChoice(player,"zhsskill","zhiliaoshu+shanxian+pingzhang+yinran+xuruo+cancel")
					if resultzh == "zhiliaoshu" then
						room:attachSkillToPlayer(player,"kezhiliaoshu")
						--room:broadcastSkillInvoke("kezhiliaoshu")
						room:addPlayerMark(player, "@kezhiliaoshu")
					end
					if resultzh == "shanxian" then
						room:attachSkillToPlayer(player,"keshanxian")
						room:broadcastSkillInvoke("keshanxian")
						room:addPlayerMark(player, "@keshanxian")
					end
					if resultzh == "pingzhang" then
						room:attachSkillToPlayer(player,"kepingzhang")
						room:broadcastSkillInvoke("kepingzhang")
						room:addPlayerMark(player, "@kepingzhang")
					end
					if resultzh == "yinran" then
						room:attachSkillToPlayer(player,"keyinran")
						room:broadcastSkillInvoke("keyinran",math.random(1,4))
						room:addPlayerMark(player, "@keyinran")
					end
				
					if resultzh == "xuruo" then
						room:attachSkillToPlayer(player,"kexuruo")
						room:broadcastSkillInvoke("kexuruo")
						room:addPlayerMark(player, "@kexuruo")
					end
		
					if resultzh == "cancel" then
		
					end
		
				end
				--其他
				if result == "arcane" then 
					local resultar = room:askForChoice(player,"arcaneskill","powder+shining+benso+police+cancel")
					if resultar == "powder" then
						room:attachSkillToPlayer(player,"kepowderthing")
						--player:gainMark("@jinxthing",2)
						room:addPlayerMark(player, "@jinxthing",2)
					end
					if resultar == "shining" then
						room:attachSkillToPlayer(player,"keshining")
						--player:gainMark("@shining")
						room:addPlayerMark(player, "@shining")
					end
					if resultar == "benso" then
						room:attachSkillToPlayer(player,"bensos")
						room:broadcastSkillInvoke("bensos")
					end
					if resultar == "police" then
						room:attachSkillToPlayer(player,"kepolice")
						room:broadcastSkillInvoke("kepolice")
					end
					if resultar == "cancel" then
		
					end
		
				end
			end
		end
	end,
	priority = 4,
}
if not sgs.Sanguosha:getSkill("kegiftsystem") then skills:append(kegiftsystem) end


--电刑

kedianxing = sgs.CreateTriggerSkill{
	name = "kedianxing&",
	events = {sgs.TargetSpecified},
	global = true,
	frequency = sgs.Skill_Limited,
	limit_mark = "@dianxing",
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if (event == sgs.TargetSpecified) and (player:getPhase() == sgs.Player_Play) then
			for _, p in sgs.qlist(use.to) do
				if (p:objectName() ~= player:objectName()) and not (use.card:isKindOf("SkillCard")) then
					if (player:getMark("&dianxingnum") < 3 and not player:hasFlag("donedianxing")) then
				        p:gainMark("&dianxingnum")
					end
				end
				if (p:getMark("&dianxingnum") == 3) and (player:getMark("@dianxing") > 0) and (p:objectName() ~= player:objectName()) and not player:hasFlag("donedianxing") then
					local to_data = sgs.QVariant()
					to_data:setValue(p)
					local will_use = room:askForSkillInvoke(player, self:objectName(), to_data)
					if will_use then
						--[[local log = sgs.LogMessage()
						log.type = "$kedianxingm"
						log.from = player
						room:sendLog(log)]]
						room:broadcastSkillInvoke(self:objectName())
						room:setEmotion(p, "Arcane/dianxing")
						local damage = sgs.DamageStruct()
						damage.from = player
						damage.to = p
						damage.damage = 1
						damage.nature = sgs.DamageStruct_Thunder
						room:damage(damage)
						room:setPlayerFlag(player, "donedianxing")
						for _, p in sgs.qlist(room:getAllPlayers()) do
							if p:getMark("&dianxingnum")>0 then
								p:loseAllMarks("&dianxingnum")
							end
						end
						player:loseAllMarks("@dianxing")
					end
				end
			end
		end
		--return false
	end,
	can_trigger = function(self, player)
	    return player:hasSkill("kedianxing")
	end,
}
if not sgs.Sanguosha:getSkill("kedianxing") then skills:append(kedianxing) end

kedianxingclear = sgs.CreateTriggerSkill{
	name = "kedianxingclear",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
	    if player:getPhase() == sgs.Player_Finish then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:getMark("&dianxingnum")>0 then
					--p:loseAllMarks("&dianxingnum")
					local num = p:getMark("&dianxingnum")
					room:removePlayerMark(p,"&dianxingnum",num)
				end
			end
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:hasSkill("kedianxing") and (p:getMark("@dianxing") == 0)  then
					room:addPlayerMark(p, "@dianxing")
				end
			end		
	    end
	end,
	can_trigger = function(self, player)
		return true
	end,
 }
 if not sgs.Sanguosha:getSkill("kedianxingclear") then skills:append(kedianxingclear) end

--致命节奏
kezhimingjiezou = sgs.CreateTriggerSkill{
	name = "kezhimingjiezou&",
	events = {sgs.Damage},
	global = true,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local eny = damage.to
		if player:hasSkill("kezhimingjiezou") and (damage.card:isKindOf("Slash"))  and (player:getPhase() == sgs.Player_Play) and not player:hasFlag("donezhimingjiezou") then
			--player:gainMark("&zhimingjiezouing")
			room:addPlayerMark(player, "&zhimingjiezouing")
			room:removePlayerMark(player, "@kezhimingjiezou")
			--player:loseAllMarks("@kezhimingjiezou")
			player:drawCards(1)
			room:setPlayerFlag(player, "donezhimingjiezou")
			room:setEmotion(player, "Arcane/zhimingjiezou")
			room:broadcastSkillInvoke(self:objectName())
			room:addSlashCishu(player,1,true)
		end
		return false
	end,
	can_trigger = function(self, player)
	    return (player:hasSkill("kezhimingjiezou")) and (player:getMark("@kezhimingjiezou")>0)
	end,
}
if not sgs.Sanguosha:getSkill("kezhimingjiezou") then skills:append(kezhimingjiezou) end


--结束阶段清除
kezhimingjiezouclear = sgs.CreateTriggerSkill{
	name = "kezhimingjiezouclear",
	frequency = sgs.Skill_Frequent,
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	   local room = player:getRoom()
	   if player:getPhase() == sgs.Player_Finish then
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if p:getMark("&zhimingjiezouing")>0 then -- player:hasFlag("doneqianggong")
				--p:loseAllMarks("&zhimingjiezouing")
				room:removePlayerMark(p,"&zhimingjiezouing")
			end
			if p:hasFlag("donezhimingjiezou") then
				room:setPlayerFlag(p, "-donezhimingjiezou")
			end
		end
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if p:hasSkill("kezhimingjiezou") and (p:getMark("@kezhimingjiezou") == 0)  then
				--p:gainMark("@kezhimingjiezou")
				room:addPlayerMark(p, "@kezhimingjiezou")
			end
		end
	end
	end,
	can_trigger = function(self, player)
		return true
	end,
 }
 if not sgs.Sanguosha:getSkill("kezhimingjiezouclear") then skills:append(kezhimingjiezouclear) end

--饼干配送
kebingganpeisong = sgs.CreateTriggerSkill{
    name = "kebingganpeisong&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@binggan",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		--local change = data:toPhaseChange()
		--if change.to ~= sgs.Player_Start then
		--	return false
		--end
		local js = room:getTag("TurnLengthCount"):toInt()
		if (player:getMark("@binggan") > 0)  then
			--奇数次
			if (js % 2 ~= 0) then
				player:drawCards(1)
				room:removePlayerMark(player, "@binggan")
				local log = sgs.LogMessage()
			    log.type = "$bingganpeisong"
			    log.from = player
			    room:sendLog(log)
			end
			--偶数次
			if (js % 2 == 0) then
				if not player:isWounded() then
					player:drawCards(1)
				end
				if player:isWounded() then
				    room:recover(player, sgs.RecoverStruct())
				end
				room:removePlayerMark(player, "@binggan")
				local log = sgs.LogMessage()
			    log.type = "$bingganpeisong"
			    log.from = player
			    room:sendLog(log)
			end
		end
		--if (player:getMark("kebinggan") == 4 )then
		--	player:loseAllMarks("kebinggan")
		--	player:gainMark("donebinggan")
		--end
	end,
	can_trigger = function(self, player)
		return player:hasSkill("kebingganpeisong")
		      and (player:getPhase() == sgs.Player_Start)
	end,
	priority = -1,
}
if not sgs.Sanguosha:getSkill("kebingganpeisong") then skills:append(kebingganpeisong) end

--爆爆的小玩意

kepowderthingCard = sgs.CreateSkillCard{
	name = "kepowderthingCard",
	target_fixed = true,
	will_throw = true,
	on_use = function(self, room, player, targets)
		local room = player:getRoom()
		room:doSuperLightbox("kejinxthing", "kepowderthing")
		player:loseMark("@jinxthing")
	--获取所有其他人
		local victims = sgs.SPlayerList()
		for _, p in sgs.qlist(room:getOtherPlayers(player)) do   
			victims:append(p)     
		end
	--获取人数
		local num = victims:length()
	--生成随机数ran
		local ran = math.random(1,num)
		room:broadcastSkillInvoke("kejinxmark", 18) 
		room:setEmotion(victims:at(ran - 1), "Arcane/jinxmark")
		local damage = sgs.DamageStruct() 
		damage.from = player
	--对其中第ran个目标造成伤害
		damage.to = victims:at(ran - 1)
		damage.damage = 1
		damage.nature = sgs.DamageStruct_Fire
		room:damage(damage)
	end
}

--爆爆炸弹
kepowderthing = sgs.CreateViewAsSkill{
	name = "kepowderthing&",
	n = 0,
	view_as = function(self, cards)
		return kepowderthingCard:clone()
	end,
	enabled_at_play = function(self, player)
		return ( (player:getMark("@jinxthing") > 0) and not player:hasUsed("#kepowderthingCard")) 
	end, 
}
if not sgs.Sanguosha:getSkill("kepowderthing") then skills:append(kepowderthing) end

--秒表

--结束阶段使用(主体)
kejinshenend = sgs.CreateTriggerSkill{
    name = "kejinshenend&",
	global = true,
	frequency = sgs.Skill_Limited,
	limit_mark = "@kemiaobiao",
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_Finish then
			return false
		end
		if room:askForSkillInvoke(player, self:objectName(), data) then
			room:removePlayerMark(player,"@kemiaobiao")
			room:addPlayerMark(player,"@brokenmiaobiao")
			room:broadcastSkillInvoke("kejinshenend")
			room:addPlayerMark(player,"&jsstop")
			player:drawCards(1)
		end
	end,
	can_trigger = function(self, player)
		return  (player:hasSkill("kejinshenend")and (player:getMark("@kemiaobiao") > 0))    
	end
}
if not sgs.Sanguosha:getSkill("kejinshenend") then skills:append(kejinshenend) end

--伤害无效化
kejinshendamage = sgs.CreateTriggerSkill{
	name = "kejinshendamage",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
	    return true 
	end,
	can_trigger = function(self, player)
		return (player:getMark("&jsstop") > 0)
	end
}
if not sgs.Sanguosha:getSkill("kejinshendamage") then skills:append(kejinshendamage) end

--不能成为目标
kejinshentarget = sgs.CreateProhibitSkill{
	name = "kejinshentarget",
	global = true,
	is_prohibited = function(self, from, to, card)
		return ((to:getMark("&jsstop") > 0) and not (card:isKindOf("SkillCard")))
	end
}
if not sgs.Sanguosha:getSkill("kejinshentarget") then skills:append(kejinshentarget) end

--回合开始取消金身
kejinshendel = sgs.CreateTriggerSkill{
    name = "kejinshendel",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_Start then
			return false
		end
		room:removePlayerMark(player,"&jsstop")
		--player:loseAllMarks("&jsstop")
	end,
	can_trigger = function(self, player)
		return player and (player:getMark("&jsstop") > 0)
	end
}
if not sgs.Sanguosha:getSkill("kejinshendel") then skills:append(kejinshendel) end


--微光

keshiningCard = sgs.CreateSkillCard{
	name = "keshiningCard",
	target_fixed = true,
	will_throw = true,
	on_use = function(self, room, player, targets)
		local room = player:getRoom()
		room:doSuperLightbox("keweiguang", "keshining")
		room:addPlayerMark(player,"&shining")
		--标记用于统计暴击几率
		--隐藏标记用于标记扣血
		room:setPlayerMark(player,"shining",1)
		room:setPlayerMark(player,"@shining",0)
		room:setEmotion(player, "Arcane/shining")
		--room:askForUseCard(player,"@@keshining_slash","shiningslash-ask")	
		local targets1 = sgs.SPlayerList()
		local list = room:getAlivePlayers()
		for _,target in sgs.qlist(list) do
			if player:canSlash(target, nil, false) then
				targets1:append(target)
			end
		end
		local target = room:askForPlayerChosen(player, targets1, "shiningslash-ask")
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		--slash:setSkillName("keshining")
		local card_use = sgs.CardUseStruct()
		card_use.card = slash
		card_use.from = player
		card_use.to:append(target)
		room:useCard(card_use, false)
		slash:deleteLater() 
	end
}

keshiningda = sgs.CreateTriggerSkill{
	name = "keshiningda",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local num = player:getMark("&shining")
		local bj = math.random(1,100)
		--[[if ( ( (bj > 90) and (num == 1) ) or ( (bj > 80) and (num == 2) ) or ( (bj > 70) and (num == 3) )
		 or ( (bj > 60) and (num == 4) ) or ( (bj > 50) and (num == 5) ) or ( (bj > 40) and (num == 6) ) 
		or ( (bj > 30) and (num == 7) ) or ( (bj > 20) and (num == 8) ) or ( (bj > 10) and (num == 9) )
	    or ( (bj > 0) and (num >= 10) )) then]]
		if ( ( (bj > 80) and (num == 1) ) or ( (bj > 60) and (num == 2) ) or ( (bj > 40) and (num == 3) )
			or ( (bj >20) and (num == 4) ) or ( (bj > 0) and (num == 5) )) then
			local hurt = damage.damage                               
			damage.damage = hurt + 1
			data:setValue(damage)
			local log = sgs.LogMessage()
			log.type = "$keweiguanglog"
			log.from = player
			room:sendLog(log)
			room:setEmotion(damage.to, "Arcane/silcosilang")
		end
	end,
	can_trigger = function(self, player)
		--有暴击率才触发
		return player and (player:getMark("&shining")>0)
	end,
}
if not sgs.Sanguosha:getSkill("keshiningda") then skills:append(keshiningda) end

--主体
keshining = sgs.CreateViewAsSkill{
	name = "keshining&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@shining",
	n = 0,
	view_as = function(self, cards)
		return keshiningCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not(player:hasUsed("#keshiningCard")) 
	end, 
}
if not sgs.Sanguosha:getSkill("keshining") then skills:append(keshining) end

--清除微光
keshiningclear = sgs.CreateTriggerSkill{
	name = "keshiningclear",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
	   local room = player:getRoom()
	   if player:getPhase() == sgs.Player_Play then
			if (player:getMark("shining") > 0) then
				local wg = room:askForChoice(player,"weiguangxuanze","losehp+losemaxhp")
			    if wg == "losehp" then
				    room:loseHp(player, 1)
					if not player:isKongcheng() then
						local card = player:getRandomHandCard()
						room:throwCard(card, player, player)
					end
				end
				if wg == "losemaxhp" then
					room:loseMaxHp(player,1)
				end
				room:setPlayerMark(player,"shining",0)
				room:setPlayerMark(player,"@shining",1)
			end
		end
	end,
	can_trigger = function(self, player)
		return player and player:hasSkill("keshining")
	end,
 }
 if not sgs.Sanguosha:getSkill("keshiningclear") then skills:append(keshiningclear) end

--强攻
keqianggong = sgs.CreateTriggerSkill{
	name = "keqianggong&",
	events = {sgs.DamageComplete,sgs.Damaged},
	global = true,
	frequency = sgs.Skill_Limited,
	limit_mark = "@keqianggong",
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Damaged) and (player:getMark("&beqianggong") > 0)then
			local damage = data:toDamage()
			local from = damage.from
			local data = sgs.QVariant()
			data:setValue(damage)
			room:sendCompulsoryTriggerLog(player,self:objectName())
			if not room:askForDiscard(player, self:objectName(), 1, 1, false, true, "@qianggong-discard") then
				local cards = player:getCards("he")
				local c = cards:at(math.random(0, cards:length() - 1))
				room:throwCard(c, player)
			end
		end
		if (event == sgs.DamageComplete) then
			local damage = data:toDamage()
			local qgz = damage.from
			if qgz then
				if (qgz:hasSkill("keqianggong")) and (qgz:getMark("@keqianggong")>0) then
					local damage = data:toDamage()
					local eny = damage.to
					if qgz:hasSkill("keqianggong") and (qgz:getPhase() == sgs.Player_Play) and (eny:isAlive()) and (not qgz:hasFlag("doneqianggong")) then
						local to_data = sgs.QVariant()
						to_data:setValue(eny)
						local will_use = room:askForSkillInvoke(qgz, self:objectName(), to_data)
						if will_use then
							room:setPlayerFlag(qgz, "doneqianggong")
							room:removePlayerMark(qgz,"@keqianggong")
							room:broadcastSkillInvoke(self:objectName())
							room:setEmotion(eny, "Arcane/qianggong")
							eny:gainMark("&beqianggong")
						end
					end
				end
			end
		end	
	end,
	can_trigger = function(self, player)
	    return player
	end,
}
if not sgs.Sanguosha:getSkill("keqianggong") then skills:append(keqianggong) end

--[[keqianggongqp = sgs.CreateTriggerSkill{
	name = "keqianggongqp",
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.Damaged},
	global = true,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local from = damage.from
		local room = player:getRoom()
		local data = sgs.QVariant()
		data:setValue(damage)
		if not room:askForDiscard(player, self:objectName(), 1, 1, false, true, "@qianggong-discard") then
			local cards = player:getCards("he")
			local c = cards:at(math.random(0, cards:length() - 1))
			room:throwCard(c, player)
		end
	end,
	can_trigger = function(self, player)
	    return (player:getMark("&beqianggong") > 0)
	end,
}
if not sgs.Sanguosha:getSkill("keqianggongqp") then skills:append(keqianggongqp) end]]

keqianggongclear = sgs.CreateTriggerSkill{
	name = "keqianggongclear",
	frequency = sgs.Skill_Frequent,
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (player:getPhase() == sgs.Player_Finish) then
		    for _, p in sgs.qlist(room:getAllPlayers()) do
			    if p:hasFlag("doneqianggong") then
					room:setPlayerFlag(p, "-doneqianggong")
				end
			end
			for _, pp in sgs.qlist(room:getAllPlayers()) do
			    if pp:hasSkill("keqianggong") and (pp:getMark("@keqianggong") == 0) then
					room:addPlayerMark(pp,"@keqianggong")
					--pp:gainMark("@keqianggong")
				end
			end
		end
		if (player:getPhase() == sgs.Player_Start) and (player:hasSkill("keqianggong"))  then
		    for _, p in sgs.qlist(room:getAllPlayers()) do
			    if p:getMark("&beqianggong")>0 then
					room:removePlayerMark(p,"&beqianggong")
				    --p:loseAllMarks("&beqianggong")
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
 }
 if not sgs.Sanguosha:getSkill("keqianggongclear") then skills:append(keqianggongclear) end

--治疗术
kezhiliaoshuCard = sgs.CreateSkillCard{
	name = "kezhiliaoshuCard",
	target_fixed = true,
	mute = true,
	on_use = function(self, room, source, targets)
		local who = room:getCurrentDyingPlayer()
		if (source:getMark("@kezhiliaoshu") > 0) and ( who or (source:getHp()<=0) ) then
			room:broadcastSkillInvoke("kezhiliaoshu")
			if (who:objectName() ~= source:objectName()) then
				room:recover(source, sgs.RecoverStruct())
				source:drawCards(1)
			end
			room:recover(who, sgs.RecoverStruct())
			who:drawCards(1)
			room:removePlayerMark(source, "@kezhiliaoshu")
			--source:loseAllMarks("@kezhiliaoshu")
		end
		if (source:getMark("@kezhiliaoshu") > 0) and not who then
			local rec = room:askForPlayerChosen(source, room:getAllPlayers(), self:objectName(), "zhiliaoshu-ask", true, true)
			if rec then
				room:broadcastSkillInvoke("kezhiliaoshu")
				if rec:objectName() ~= source:objectName() then
					room:recover(source, sgs.RecoverStruct())
					source:drawCards(1)
				end
				room:recover(rec, sgs.RecoverStruct())
				rec:drawCards(1)
				--source:loseAllMarks("@kezhiliaoshu")
				room:removePlayerMark(source, "@kezhiliaoshu")
			end
		end
	end
}
kezhiliaoshu = sgs.CreateZeroCardViewAsSkill{
	name = "kezhiliaoshu&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@kezhiliaoshu",
	view_as = function(self) 
		return kezhiliaoshuCard:clone()
	end, 
	enabled_at_play = function(self, player)
		return (player:getMark("@kezhiliaoshu") > 0)
	end, 
	enabled_at_response = function(self, player, pattern)
		return string.find(pattern, "peach") and (player:getMark("@kezhiliaoshu")>0) 
	end,
	can_trigger = function(self, player)
		return (player:getMark("@kezhiliaoshu") > 0)
	end
}
if not sgs.Sanguosha:getSkill("kezhiliaoshu") then skills:append(kezhiliaoshu) end

--闪现

keshanxianbe = sgs.CreateTriggerSkill{
	name = "keshanxianbe",
	frequency = sgs.Skill_NotFrequent,
	global = true,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if player:askForSkillInvoke(self:objectName(), data) then
			room:removePlayerMark(player,"@keshanxian")
			--player:loseMark("@keshanxian")		
		    local players = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getAllPlayers()) do
			    if p:isAdjacentTo(player) then
					players:append(p)
				end
			end
			local ecp = room:askForPlayerChosen(player, players, self:objectName(), "keshanxian-ask")
			local me = player
			room:swapSeat(ecp, me)		
			room:broadcastSkillInvoke("keshanxian")
			room:addDistance(player, 1, false, true)
	        return true 
		end
		return false
	end,
	can_trigger = function(self, player)
		return ( (player:hasSkill("keshanxian"))  and (player:getMark("@keshanxian") > 0)   ) 
	end
}
if not sgs.Sanguosha:getSkill("keshanxianbe") then skills:append(keshanxianbe) end

--主动闪现
keshanxianCard = sgs.CreateSkillCard{
	name = "keshanxianCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		return (#targets == 0) and (to_select:isAdjacentTo(sgs.Self))
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:removePlayerMark(player,"@keshanxian")
		room:swapSeat(target, player)	
		room:addPlayerMark(player,"&shanxianeffect")	
		--player:gainMark("&shanxianeffect")
		room:addDistance(player, -1, true, true)
	end
}

keshanxian = sgs.CreateViewAsSkill{
	name = "keshanxian&",
	n = 0,
	frequency = sgs.Skill_Limited,
	limit_mark = "@keshanxian",
	view_as = function(self, cards)
		return keshanxianCard:clone()
	end,
	enabled_at_play = function(self, player)
		return ( player:getMark("@keshanxian")>0)
	end, 
}
if not sgs.Sanguosha:getSkill("keshanxian") then skills:append(keshanxian) end

--不能响应
keshanxianxiangying = sgs.CreateTriggerSkill{
    name = "keshanxianxiangying",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TargetSpecified},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if (use.card:isKindOf("Slash") or use.card:isNDTrick()) and use.card:isDamageCard() then
			local no_respond_list = use.no_respond_list
			for _, szm in sgs.qlist(use.to) do
				table.insert(no_respond_list, szm:objectName())
			end
			use.no_respond_list = no_respond_list
			data:setValue(use)
			room:removePlayerMark(player,"&shanxianeffect")
			--player:loseAllMarks("&shanxianeffect")
		end
	end,
	can_trigger = function(self, player)
	    return (player:getMark("&shanxianeffect")>0)
	end,
}
if not sgs.Sanguosha:getSkill("keshanxianxiangying") then skills:append(keshanxianxiangying) end

--屏障

kepingzhang = sgs.CreateTriggerSkill{
	name = "kepingzhang&",
	global = true,
	frequency = sgs.Skill_Limited,
	limit_mark = "@kepingzhang",
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		if event == sgs.DamageInflicted then
			local damage = data:toDamage()
			local room = player:getRoom()
			if player:askForSkillInvoke(self:objectName(), data) then
				room:broadcastSkillInvoke("kepingzhang")
				room:removePlayerMark(player, "@kepingzhang")
				--player:loseAllMarks("@kepingzhang")
				player:gainHujia(3)
				--local lo = player:getLostHp()
				--player:drawCards(lo)
				room:setPlayerFlag(player,"usedpingzhang")
			end
		end
	end,
	can_trigger = function(self, player)
		return (  (player:hasSkill("kepingzhang")) and (player:getMark("@kepingzhang") > 0) )  
	end
}
if not sgs.Sanguosha:getSkill("kepingzhang") then skills:append(kepingzhang) end

--结束失去护甲
kepingzhangclear = sgs.CreateTriggerSkill{
	name = "kepingzhangclear",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
	    if player:getPhase() == sgs.Player_Finish then
		    for _, p in sgs.qlist(room:getAllPlayers()) do
			    if p:hasFlag("usedpingzhang") then
					--p:drawCards(1)
				    p:loseHujia(2)
					room:setPlayerFlag(p,"-usedpingzhang")
			    end
		    end
	    end
	end,
	can_trigger = function(self, player)
		return true
	end,
 }
 if not sgs.Sanguosha:getSkill("kepingzhangclear") then skills:append(kepingzhangclear) end

--引燃

keyinranbuff = sgs.CreateTriggerSkill{
	name = "keyinranbuff",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreHpRecover},
	on_trigger = function(self, event, player, data, room)
		local rec = data:toRecover()
		if not (room:getCurrentDyingPlayer()) then 
			local num = rec.recover
			room:broadcastSkillInvoke("keyinran",5)
			player:drawCards(num)
			local log = sgs.LogMessage()
			log.type = "$keyinranlog"
			log.from = player
			room:sendLog(log)
			return true
	    end
	end,
	can_trigger = function(self, player)
		return (player:getMark("&beyinraned")>0)
	end,
}
if not sgs.Sanguosha:getSkill("keyinranbuff") then skills:append(keyinranbuff) end

--引燃主技能
keyinranCard = sgs.CreateSkillCard{
	name = "keyinranCard",
	target_fixed = false,
	will_throw = false,
	mute = true,
	filter = function(self, targets, to_select)
		return #targets == 0 and (to_select:objectName() ~= sgs.Self:objectName())
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:broadcastSkillInvoke("keyinran",math.random(1,4))
		room:addPlayerMark(target,"&beyinraned")
		room:addPlayerMark(target,"yinranjishu",2)
		room:loseHp(target,1,true)
		room:removePlayerMark(player,"@keyinran")
	end
}

keyinran = sgs.CreateViewAsSkill{
	name = "keyinran&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@keyinran",
	n = 0,
	view_as = function(self, cards)
		return keyinranCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@keyinran")>0
	end, 
}
if not sgs.Sanguosha:getSkill("keyinran") then skills:append(keyinran) end

--引燃清除
keyinranclear = sgs.CreateTriggerSkill{
	name = "keyinranclear",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
	    if player:getPhase() == sgs.Player_Finish then
			if player:getMark("yinranjishu")>0 then
				room:removePlayerMark(player,"yinranjishu")
				if (player:getMark("yinranjishu") == 0) then
					room:removePlayerMark(player,"&beyinraned")
				end
			end
	    end
	end,
	can_trigger = function(self, player)
		return player:getMark("&beyinraned")>0
	end,
 }
 if not sgs.Sanguosha:getSkill("keyinranclear") then skills:append(keyinranclear) end

--虚弱

kexuruoCard = sgs.CreateSkillCard{
	name = "kexuruoCard",
	target_fixed = false,
	will_throw = false,
	mute = true,
	filter = function(self, targets, to_select)
		return #targets == 0 and (to_select:objectName() ~= sgs.Self:objectName())
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:addPlayerMark(target,"&xuruodamage",1)
		room:addPlayerMark(target,"&xuruomopai",2)
		room:removePlayerMark(player,"@kexuruo")
		room:broadcastSkillInvoke("kexuruo")
	end
}

kexuruo = sgs.CreateViewAsSkill{
	name = "kexuruo&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@kexuruo",
	n = 0,
	view_as = function(self, cards)
		return kexuruoCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@kexuruo")>0
	end, 
}
if not sgs.Sanguosha:getSkill("kexuruo") then skills:append(kexuruo) end




xuruobuff = sgs.CreateTriggerSkill{
	name = "xuruobuff",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawNCards,sgs.DamageCaused},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DrawNCards then
			local draw = data:toDraw()
			if not (draw.reason == "draw_phase") then return false end
			if player:getMark("&xuruomopai")>0 then
				local count = draw.num
				if count > 0 then
					draw.num = count - 1
					data:setValue(draw)
				end
				room:removePlayerMark(player,"&xuruomopai")
			end	
		end
		if event == sgs.DamageCaused then
			if player:getMark("&xuruodamage")>0 then
				local damage = data:toDamage()
                local room = player:getRoom()
				local log = sgs.LogMessage()
				log.type = "$kexuruolog"
				log.from = player
				room:sendLog(log)
				local sh = damage.damage
                if  sh == 1 then
					room:removePlayerMark(player,"&xuruodamage")
                    return true
                end
                if  sh > 1 then
				    damage.damage = sh -1
					room:removePlayerMark(player,"&xuruodamage")
				    data:setValue(damage)      		
			    end
			end
		end
	end,
	can_trigger = function(self, player)
		return  ((player:getMark("&xuruomopai")>0) or (player:getMark("&xuruodamage")>0))
	end,
}
if not sgs.Sanguosha:getSkill("xuruobuff") then skills:append(xuruobuff) end


--本索的杂货铺

bensoget = sgs.CreateTriggerSkill{
	name = "bensoget",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.Damaged},
	can_trigger = function(self, target)
		return target:hasSkill("bensos")
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Damaged and player:isAlive() and player:hasSkill("bensos") then
			local damage = data:toDamage()
			for i = 0, damage.damage - 1, 1 do
				if player:getMark("@kegold")<10 then
				    room:addPlayerMark(player,"@kegold")
					room:broadcastSkillInvoke("bensos")
				end
			end
		end
		return false
	end
}
if not sgs.Sanguosha:getSkill("bensoget") then skills:append(bensoget) end

bensogettwo = sgs.CreateTriggerSkill{
	name = "bensogettwo",
	events = {sgs.Damage},
	global = true,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		for i = 0, damage.damage - 1, 1 do
			if player:getMark("@kegold")<10 then
				room:addPlayerMark(player,"@kegold")
				room:broadcastSkillInvoke("bensos")
			end
		end
	end,
	can_trigger = function(self, target)
		return target:hasSkill("bensos")
	end,
}
if not sgs.Sanguosha:getSkill("bensogettwo") then skills:append(bensogettwo) end


--商店内容
bensosCard = sgs.CreateSkillCard{
	name = "bensosCard",
	target_fixed = true,
	will_throw = true,
	mute = true,
	on_use = function(self, room, player, targets)
		local room = player:getRoom()
		local class = room:askForChoice(player,"goodsclass","basiccard+effect+equip")
		--基本牌
		if class == "basiccard" then
			local bc = room:askForChoice(player,"bensojibenpai","slash+jink+wine+peach+cancel")
			if bc == "slash" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("Slash")) then
						--没有足够金币
						if (player:getMark("@kegold") < 4) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 4) then
							room:removePlayerMark(player,"@kegold",4)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:setPlayerFlag(player, "alreadybuy")
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
			if bc == "jink" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("Jink")) then
						--没有足够金币
						if (player:getMark("@kegold") < 4) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end
						--有足够的金币
						if (player:getMark("@kegold") >= 4) then
							room:removePlayerMark(player,"@kegold",4)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end	
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
			if bc == "wine" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("Analeptic")) then
						--没有足够金币
						if (player:getMark("@kegold") < 6) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 6) then
							room:removePlayerMark(player,"@kegold",6)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
			if bc == "peach" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("Peach")) then
						--没有足够金币
						if (player:getMark("@kegold") < 6) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 6) then
							room:removePlayerMark(player,"@kegold",6)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
		end
		--特效
		if class == "effect" then
			local bc = room:askForChoice(player,"bensotexiao","judge+maxhand+msg+cancel")
			if bc == "judge" then
				if (player:getMark("@kegold") < 6) then
					local log = sgs.LogMessage()
					log.type = "$kebensonomoney"
					log.from = player
					room:sendLog(log)	
					room:doLightbox("$kebensonomoney")
				end	
				if (player:getMark("@kegold") >= 6) then
					room:removePlayerMark(player,"@kegold",6)
					player:throwJudgeArea()
					local log = sgs.LogMessage()
					log.type = "$kebensobuy"
					log.from = player
					room:sendLog(log)
					room:broadcastSkillInvoke("bensos")
					room:doLightbox("$kebensobuy")
					room:setPlayerFlag(player, "alreadybuy")
				end
			end
			if bc == "maxhand" then
				if (player:getMark("@kegold") < 5) then
					local log = sgs.LogMessage()
					log.type = "$kebensonomoney"
					log.from = player
					room:sendLog(log)	
					room:doLightbox("$kebensonomoney")
				end	
				if (player:getMark("@kegold") >= 5) then
					room:removePlayerMark(player,"@kegold",5)
					room:addPlayerMark(player,"bensomaxhand")
					local log = sgs.LogMessage()
					log.type = "$kebensobuy"
					log.from = player
					room:sendLog(log)
					room:broadcastSkillInvoke("bensos")
					room:doLightbox("$kebensobuy")
					room:setPlayerFlag(player, "alreadybuy")
				end	
			end
			if bc == "msg" then
				if (player:getMark("@kegold") < 2) then
					local log = sgs.LogMessage()
					log.type = "$kebensonomoney"
					log.from = player
					room:sendLog(log)	
					room:doLightbox("$kebensonomoney")
				end	
				if (player:getMark("@kegold") >= 2) then
					room:removePlayerMark(player,"@kegold",2)
					local eny = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "bensosmsg-ask")
					room:showAllCards(eny, player)
					local log = sgs.LogMessage()
					log.type = "$kebensobuy"
					log.from = player
					room:sendLog(log)
					room:broadcastSkillInvoke("bensos")
					room:doLightbox("$kebensobuy")
					room:setPlayerFlag(player, "alreadybuy")
				end	
			end
		end
		--装备
		if class == "equip" then
			local bc = room:askForChoice(player,"bensozhuangbei","weapon+armor+addone+minusone+cancel")
			if bc == "weapon" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("Weapon")) then
						--没有足够金币
						if (player:getMark("@kegold") < 5) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 5) then
							room:removePlayerMark(player,"@kegold",5)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
			if bc == "armor" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("Armor")) then
						--没有足够金币
						if (player:getMark("@kegold") < 5) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 5) then
							room:removePlayerMark(player,"@kegold",5)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
			if bc == "addone" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("DefensiveHorse")) then
						--没有足够金币
						if (player:getMark("@kegold") < 5) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 5) then
							room:removePlayerMark(player,"@kegold",5)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
			if bc == "minusone" then
				local have = 0
				local nm = 0
				for _, id in sgs.qlist(room:getDrawPile()) do
					if (sgs.Sanguosha:getCard(id):isKindOf("OffensiveHorse")) then
						--没有足够金币
						if (player:getMark("@kegold") < 5) then
							local log = sgs.LogMessage()
							log.type = "$kebensonomoney"
							log.from = player
							room:sendLog(log)	
							room:doLightbox("$kebensonomoney")
							nm = 1
							break
						end	
						--有足够的金币
						if (player:getMark("@kegold") >= 5) then
							room:removePlayerMark(player,"@kegold",5)
							room:obtainCard(player, id, true)
							local log = sgs.LogMessage()
							log.type = "$kebensobuy"
							log.from = player
							room:sendLog(log)
							room:broadcastSkillInvoke("bensos")
							room:doLightbox("$kebensobuy")
							room:setPlayerFlag(player, "alreadybuy")
							have = 1
							break
						end
					end
				end
				if have == 0 and nm == 0 then
					local log = sgs.LogMessage()
					log.type = "$kebensonogood"
					log.from = player
					room:sendLog(log)
					room:doLightbox("$kebensonogood")
				end
			end
		end

	end
}


--主技能
bensos = sgs.CreateViewAsSkill{
	name = "bensos&",
	n = 0,
	view_as = function(self, cards)
		return bensosCard:clone()
	end,
	enabled_at_play = function(self, player)
		return (not player:hasFlag("alreadybuy"))
	end, 
}
if not sgs.Sanguosha:getSkill("bensos") then skills:append(bensos) end

--手牌上限
kebensoKeep = sgs.CreateMaxCardsSkill{
	name = "kebensoKeep",
	global = true,
	frequency = sgs.Skill_Compulsory,
	extra_func = function(self, target)
		if (target:getMark("bensomaxhand")>0) then
			return target:getMark("bensomaxhand")
		else 
			return 0
		end
	end
}
if not sgs.Sanguosha:getSkill("kebensoKeep") then skills:append(kebensoKeep) end

--行窃预兆
kestole = sgs.CreateTriggerSkill{
    name = "kestole&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@kestole",
	global = true,
	events = {sgs.TargetSpecified},
	on_trigger = function(self, event, player, data)
		if event == sgs.TargetSpecified then
			local room = player:getRoom()
			local use = data:toCardUse()
			if (use.to:length() == 1) and not ((use.to:contains(player)) or (use.card:isKindOf("SkillCard")) or (use.card:isKindOf("Peach"))  )then
				local tou = math.random(1,4)
				if tou == 1 then
					room:broadcastSkillInvoke("kestole")
					room:getThread():delay(400)
					player:drawCards(1)
					local log = sgs.LogMessage()
					log.type = "$kestoled"
					log.from = player
					room:sendLog(log)	
				end
				if (tou ~= 1) then
					local yy = use.to:at(0):getHandcardNum() + use.to:at(0):getEquips():length()
					local two = math.random(1,6)
					if (two == 1) and (yy > 0) then
						room:broadcastSkillInvoke("kestole")
						room:doAnimate(1, player:objectName(), use.to:at(0):objectName())
						room:getThread():delay(500)
						local card_id = room:askForCardChosen(player, use.to:at(0), "he", self:objectName())
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
						room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
						local log = sgs.LogMessage()
						log.type = "$kestoled"
						log.from = player
						room:sendLog(log)
					end
					if two == 1 and (yy == 0) then
						room:broadcastSkillInvoke("kestole")
						room:getThread():delay(400)
						player:drawCards(1)
						local log = sgs.LogMessage()
						log.type = "$kestoled"
						log.from = player
						room:sendLog(log)
					end
				end
			end
		end	
	end,
	can_trigger = function(self, player)
		return player:hasSkill("kestole")
	end,

}
if not sgs.Sanguosha:getSkill("kestole") then skills:append(kestole) end

--警长的指控
kepoliceCard = sgs.CreateSkillCard{
	name = "kepoliceCard" ,
	will_throw = true,
	mute = true,
	filter = function(self, targets, to_select)
		return (#targets == 0) and (to_select:objectName() ~= sgs.Self:objectName()) and (to_select:getHp() >= sgs.Self:getHp())
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		local to = room:askForPlayerChosen(player, room:getOtherPlayers(target), self:objectName(), "police-ask", true, true)
		if to then
			local damage = sgs.DamageStruct(self:objectName(), target, to, 1, sgs.DamageStruct_Normal)
            local _data = sgs.QVariant()
            _data:setValue(damage)
			room:doAnimate(1, target:objectName(), to:objectName())
			room:broadcastSkillInvoke("kepolice")
			room:getThread():delay(1000)
			room:getThread():trigger(sgs.Damage, room, target, _data)
			room:getThread():trigger(sgs.Damaged, room, to, _data)
		end
	end
}

kepolice = sgs.CreateViewAsSkill{
	name = "kepolice&" ,
	frequency = sgs.Skill_Limited,
	limit_mark = "@kepolice",
	n = 1 ,
	view_filter = function(self, selected, to_select)
		return true
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local policecard = kepoliceCard:clone()
		policecard:addSubcard(cards[1])
		return policecard
	end ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#kepoliceCard")
	end
}
if not sgs.Sanguosha:getSkill("kepolice") then skills:append(kepolice) end


--法力流系带

kefalidai = sgs.CreateTriggerSkill{
	name = "kefalidai&",
	frequency = sgs.Skill_Frequent,
	global = true,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from and (move.from:objectName() == player:objectName()) 
			and move.from_places:contains(sgs.Player_PlaceHand) and (move.from:getHandcardNum() <=1) 
			and (not move.from:hasFlag("falidaiuse")) and (move.from:getPhase() == sgs.Player_Play) then
				if room:askForSkillInvoke(player, self:objectName(), data) then
					player:drawCards(1, self:objectName())
					room:setPlayerFlag(player,"falidaiuse")
					room:addPlayerMark(player,"&kefalidai")
				end
				if player:getMark("&kefalidai") == 2 then
					room:removePlayerMark(player,"&kefalidai",2)
					room:addPlayerMark(player,"@kefalidai")
				end
			end
		end
	end,
	can_trigger = function(self, player)
	    return player and player:hasSkill(self:objectName())
	end,
}
if not sgs.Sanguosha:getSkill("kefalidai") then skills:append(kefalidai) end
--keanjiang:addSkill(kefalidai)

--加上限
kefalidaiKeep = sgs.CreateMaxCardsSkill{
	name = "kefalidaiKeep",
	frequency = sgs.Skill_Compulsory,
	extra_func = function(self, target)
		if target:hasSkill("kefalidai") then
			return target:getMark("@kefalidai")
		else
			return 0
		end
	end
}
if not sgs.Sanguosha:getSkill("kefalidaiKeep") then skills:append(kefalidaiKeep) end



kexunjiebufa = sgs.CreateTriggerSkill{
	name = "kexunjiebufa&",
	global = true,
	events = {sgs.CardFinished,sgs.TargetSpecified,sgs.CardResponded},
	frequency = sgs.Skill_Limited,
	limit_mark = "@kexunjiebufa",
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.CardFinished then
			if (use.from:objectName() == player:objectName()) and not ((use.card:isKindOf("SkillCard")) or (use.card:isKindOf("Jink"))) then 
				if (player:getMark("@kexunjiebufa") < 6) and (player:getMark("&kexunjiebufa") == 0) then
					room:addPlayerMark(player,"@kexunjiebufa")
				end
				if (player:getMark("@kexunjiebufa") == 6) then
					room:removePlayerMark(player,"@kexunjiebufa",6)
					room:addPlayerMark(player,"&kexunjiebufa")
				end
			end
		end
		if event == sgs.CardResponded then
			if (player:getMark("@kexunjiebufa") < 6) and (player:getMark("&kexunjiebufa") == 0) then
				room:addPlayerMark(player,"@kexunjiebufa")
			end
			if (player:getMark("@kexunjiebufa") == 6) then
				room:removePlayerMark(player,"@kexunjiebufa",6)
				room:addPlayerMark(player,"&kexunjiebufa")
			end		
		end
		if event == sgs.TargetSpecified then
			if (player:getMark("&kexunjiebufa") > 0) and (use.card:isKindOf("Slash") or use.card:isNDTrick()) and use.card:isDamageCard() then
				local result = room:askForChoice(player,"xunjiebufa-name","huixue+mopai")
				if result == "huixue" then
					room:recover(player, sgs.RecoverStruct())
				end
				if result == "mopai" then
					player:drawCards(1)
				end
				room:broadcastSkillInvoke(self:objectName())
				local log = sgs.LogMessage()
				log.type = "$kexunjiebufa"
				log.from = player
				room:sendLog(log)
				room:removePlayerMark(player,"&kexunjiebufa")
			end
		end
	end,
	can_trigger = function(self, player)
	    return player:hasSkill("kexunjiebufa")
	end,
}
if not sgs.Sanguosha:getSkill("kexunjiebufa") then skills:append(kexunjiebufa) end

kexunjiebufajuli = sgs.CreateDistanceSkill{
	name = "kexunjiebufajuli",
	correct_func = function(self, from)
		if (from:getMark("&kexunjiebufa") > 0) then
			return -1
		else
			return 0
		end
	end,
}
if not sgs.Sanguosha:getSkill("kexunjiebufajuli") then skills:append(kexunjiebufajuli) end



--无情猎手
kewuqinglieshouda = sgs.CreateTriggerSkill{
	name = "kewuqinglieshouda",
	events = {sgs.Damage},
	global = true,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if (player:getPhase() == sgs.Player_Play) then
			if player:getMark("wqlsdamage") == 0 then
				--player:gainMark("wqlsdamage")
				room:setPlayerMark(player, "wqlsdamage",1)
			end
		end
	end,
	can_trigger = function(self, target)
		return target:hasSkill("kewuqinglieshou") 
	end
}
if not sgs.Sanguosha:getSkill("kewuqinglieshouda") then skills:append(kewuqinglieshouda) end

kewuqinglieshou = sgs.CreateTriggerSkill{
	name = "kewuqinglieshou&",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_NotActive then
				local room = player:getRoom()
				if player:getMark("&kewuqinglieshou") > 0 then
					room:setPlayerMark(player, "&kewuqinglieshou",0)
				end
				if player:getMark("wqlsdamage") == 0 then
					room:setPlayerMark(player, "&kewuqinglieshou",1)
					local log = sgs.LogMessage()
					log.type = "$kewuqinglieshoulog"
					log.from = player
					room:sendLog(log)
				end
				if player:getMark("wqlsdamage") > 0 then
					room:setPlayerMark(player, "wqlsdamage",0)
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target:hasSkill(self:objectName()) 
	end
}
if not sgs.Sanguosha:getSkill("kewuqinglieshou") then skills:append(kewuqinglieshou) end

kewuqinglieshoujuli = sgs.CreateDistanceSkill{
	name = "kewuqinglieshoujuli",
	correct_func = function(self, from)
		if (from:getMark("&kewuqinglieshou") > 0) then
			return -2
		else
			return 0
		end
	end,
}
if not sgs.Sanguosha:getSkill("kewuqinglieshoujuli") then skills:append(kewuqinglieshoujuli) end

--黑暗收割
kedarkshouge = sgs.CreateTriggerSkill{
	name = "kedarkshouge&",
	events = {sgs.Damage},
	global = true,
	frequency = sgs.Skill_Limited,
	limit_mark = "@kedarkshouge",
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Damage then
			local damage = data:toDamage()
			local eny = damage.to
			local me = damage.from
			if me:hasSkill(self:objectName()) and (eny:getHp() <= (eny:getMaxHp())/2) and (eny:isAlive())
			and (eny:objectName() ~= me:objectName()) and (damage.reason ~= "kedarkshouge") then
				local log = sgs.LogMessage()
				log.type = "$kedarkshougelog"
				log.from = me
				room:sendLog(log)
				room:broadcastSkillInvoke(self:objectName(),2)
				room:addPlayerMark(me,"@kedarkshouge")
				local to_data = sgs.QVariant()
				to_data:setValue(eny)
				local will_use = room:askForSkillInvoke(me, self:objectName(), to_data)
				if will_use then
					if eny:canDiscard( eny, "he") then
						local to_throw = room:askForCardChosen(me, eny, "he", self:objectName())
						local card = sgs.Sanguosha:getCard(to_throw)
						room:throwCard(card, eny, me);
					end
					if player:getMark("@kedarkshouge") >= 3 then
						room:removePlayerMark(me,"@kedarkshouge",3)
						room:broadcastSkillInvoke(self:objectName(),1)
						room:damage(sgs.DamageStruct("kedarkshouge", me, eny))
					end
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target:hasSkill(self:objectName()) 
	end
}
if not sgs.Sanguosha:getSkill("kedarkshouge") then skills:append(kedarkshouge) end

--守护者
keshouhuzhe = sgs.CreateTriggerSkill{
	name = "keshouhuzhe&",
	frequency = sgs.Skill_Limited,
	limit_mark = "@keshouhuzhe",
	global = true,
	events = {sgs.Damaged,sgs.RoundStart},--sgs.EventPhaseStart
	on_trigger = function(self, event, player, data)
		if event == sgs.RoundStart and player:hasSkill(self:objectName()) then
			local room = player:getRoom()
			for _, c in sgs.qlist(room:getAllPlayers()) do    
				if c:getMark("&keshouhuzhebaohu") > 0 then
					room:setPlayerMark(c,"@keshouhuzhe",0)
					room:setPlayerMark(c,"&keshouhuzhebaohu",0)
				end
				if c:getMark("alreadyshouhuzhe") > 0 then
					c:loseHujia(1)
					room:setPlayerMark(c,"alreadyshouhuzhe",0)
				end
				if c:getMark("shouhuzhemopai") > 0 then
					room:setPlayerMark(c,"shouhuzhemopai",0)
				end
			end
			--以上是清除工作
			local fri = room:askForPlayerChosen(player, room:getAllPlayers(), self:objectName(), "keshouhuzhe-ask", true, true)
			if fri then
				room:setPlayerMark(fri,"&keshouhuzhebaohu",1)
				room:setPlayerMark(player,"&keshouhuzhebaohu",1)
			end
			if not fri then
				room:setPlayerMark(player,"&keshouhuzhebaohu",1)
			end
		end
		if event == sgs.Damaged then
			local room = player:getRoom()
			local damage = data:toDamage()
			local me = damage.to
			local benren = room:findPlayerBySkillName("keshouhuzhe")
			if benren then
				for i = 0, damage.damage - 1, 1 do
					if me:getMark("&keshouhuzhebaohu") > 0 then
						room:addPlayerMark(benren,"@keshouhuzhe")
					end
				end
				if (benren:getMark("@keshouhuzhe") >= 2) and (benren:getMark("shouhuzhemopai") == 0) then
					local log = sgs.LogMessage()
					log.type = "$keshouhuzhelogmopai"
					log.from = benren
					room:sendLog(log)
					room:getThread():delay(500)
					for _, c in sgs.qlist(room:getAllPlayers()) do    
						if c:getMark("&keshouhuzhebaohu") > 0 then
							c:drawCards(1)
							room:broadcastSkillInvoke("keshouhuzhe")
							room:setPlayerMark(benren,"shouhuzhemopai",1)
						end
					end
				end
				if benren:getMark("@keshouhuzhe") >= 3 then
					local log = sgs.LogMessage()
					log.type = "$keshouhuzhelog"
					log.from = benren
					room:sendLog(log)
					room:getThread():delay(500)
					for _, c in sgs.qlist(room:getAllPlayers()) do    
						if c:getMark("&keshouhuzhebaohu") > 0 then
							c:gainHujia(1)
							room:broadcastSkillInvoke("keshouhuzhe")
							room:setPlayerMark(c,"&keshouhuzhebaohu",0)
							room:setPlayerMark(c,"alreadyshouhuzhe",1)
						end
					end
					room:setPlayerMark(benren,"@keshouhuzhe",0)
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}
if not sgs.Sanguosha:getSkill("keshouhuzhe") then skills:append(keshouhuzhe) end








--符文一览

kegift = sgs.General(extension, "kegift", "god", 0, true)
kegift:setGender(sgs.General_Sexless)

kegiftchange = sgs.CreateTriggerSkill{
	name = "#kegiftchange",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.GameReady},
	on_trigger = function(self, event, player, data)
		if event == sgs.GameReady then
			if (player:getGeneralName() == "kegift") then 
				room:changeHero(player, "ol_caocao", false, false, false, false)
			end
			if (player:getGeneral2Name() == "kegift") then
				room:changeHero(player, "ol_caocao", false, false, true, false)
			end
		end
	end,
}
kegift:addSkill(kegiftchange)

kegift:addRelateSkill("kepowderthing")
kegift:addRelateSkill("kebingganpeisong")
kegift:addRelateSkill("keshining")
kegift:addRelateSkill("keqianggong")
kegift:addRelateSkill("kezhimingjiezou")
kegift:addRelateSkill("kedianxing")
kegift:addRelateSkill("kezhiliaoshu")
kegift:addRelateSkill("keshanxian")
kegift:addRelateSkill("kepingzhang")
kegift:addRelateSkill("keyinran")
kegift:addRelateSkill("kexuruo")
kegift:addRelateSkill("bensos")
kegift:addRelateSkill("kestole")
kegift:addRelateSkill("kepolice")
kegift:addRelateSkill("kejinshenend")
kegift:addRelateSkill("kefalidai")
kegift:addRelateSkill("kexunjiebufa")
kegift:addRelateSkill("kewuqinglieshou")
kegift:addRelateSkill("kedarkshouge")
kegift:addRelateSkill("keshouhuzhe")


sgs.Sanguosha:addSkills(skills)
sgs.LoadTranslationTable{
    ["kefuwensys"] = "符文系统",

	--符文系统
	    --符文一览
				["kegift"] = "符文一览", 
				["&kegift"] = "符文一览",
				["#kegift"] = "",
				["designer:kegift"] = "小珂酱",
				["cv:kegift"] = "官方",
				["illustrator:kegift"] = "官方",

		--开局选择：
		["typeofgift"] = "选择符文系", 
            --精密系

			    ["jingmigift"] = "选择精密符文", 
				["typeofgift:jingmi"] = "-=精密系=-",
				["jingmigift:qianggong"] = "强攻",
				["jingmigift:zhimingjiezou"] = "致命节奏",
				["jingmigift:xunjiebufa"] = "迅捷步法",
				["jingmigift:kaixuan"] = "凯旋",

            --主宰系
			    ["zhuzaigift"] = "选择主宰符文", 
				["typeofgift:zhuzai"] = "-=主宰系=-",
				["zhuzaigift:dianxing"] = "电刑",
				["zhuzaigift:darkshouge"] = "黑暗收割",
				["zhuzaigift:wqls"] = "无情猎手",

			--巫术系
			    ["wushugift"] = "选择巫术符文", 
				["typeofgift:wushu"] = "-=巫术系=-",
				["wushugift:falidai"] = "法力流系带",

			--坚决系
			    ["jianjuegift"] = "选择坚决符文", 
				["typeofgift:jianjue"] = "-=坚决系=-",
				["jianjuegift:shouhuzhe"] = "守护者",

			--启迪系
			    ["qidigift"] = "选择启迪符文", 
				["typeofgift:qidi"] = "-=启迪系=-",
				["qidigift:stole"] = "行窃预兆",
				["qidigift:binggan"] = "饼干配送",
				["qidigift:miaobiao"] = "完美时机",

			--召唤师技能
			    ["zhsskill"] = "选择召唤师技能", 
				["typeofgift:zhsskill"] = "-=召唤师技能=-",
				["zhsskill:zhiliaoshu"] = "治疗术",
				["zhsskill:shanxian"] = "闪现",
				["zhsskill:pingzhang"] = "屏障",
				["zhsskill:yinran"] = "引燃",
				["zhsskill:xuruo"] = "虚弱",

				


			--道具
			    ["arcaneskill"] = "选择道具", 
				["typeofgift:arcane"] = "-=双城之战=-",
				["arcaneskill:powder"] = "爆爆的小玩意",
				["arcaneskill:shining"] = "微光",
				["arcaneskill:benso"] = "本索杂货铺",
				["arcaneskill:police"] = "警长的指控",

				


        --具体内容

		    --电刑
		        ["kedianxing"] = "电刑",
				["@dianxing"] = "电刑",
				[":kedianxing"] = "出牌阶段限一次，当你于出牌阶段使用牌第三次指定一名其他角色为目标后，你可以对其造成1点雷电伤害。",
				["kedianxingclear"] = "电刑",
				["dianxingnum"] = "电刑累计",
            --致命节奏
				["kezhimingjiezou"] = "节奏",
				["@kezhimingjiezou"] = "致命节奏",
				[":kezhimingjiezou"] = "锁定技，出牌阶段限一次，当你使用【杀】造成伤害后，你摸一张牌且本阶段你使用【杀】的次数限制+1。",
				["kezhimingjiezouqp"] = "节奏",
				["kezhimingjiezouclear"] = "节奏",
				["zhimingjiezouing"] = "致命节奏",

			--治疗术
				["kezhiliaoshu"] = "治疗术",
				[":kezhiliaoshu"] = "限定技，一名角色进入濒死状态时，或出牌阶段你可以选择一名角色，你令其回复1点体力并摸一张牌，若这名角色不是你，你回复1点体力并摸一张牌。",
				["kezhiliaoshuCard"] = "治疗术",
				["@kezhiliaoshu"] = "治疗术",
				["zhiliaoshu-ask"] = "请选择治疗的目标",

			--闪现
				["keshanxian"] = "闪现",
				["keshanxianbe"] = "闪现",
				["keshanxianxiangying"] = "闪现",
				["shanxianeffect"] = "闪现",
				[":keshanxian"] = "限定技，\
				◆当你受到伤害时,你可以与你的上家或下家交换座次，然后防止你受到的伤害且其他角色与你的距离+1直到你下个回合结束。\
				◆出牌阶段，你可以与你的上家或下家交换座次，若如此做，你本回合与其他角色的距离-1且你使用的下一张伤害类牌不能被响应。",
				["keshanxianCard"] = "闪现",
				["@keshanxian"] = "闪现",
				["keshanxian-ask"] = "请选择闪现的位置",

		    --屏障
				["kepingzhang"] = "屏障",
				[":kepingzhang"] = "限定技，当你受到伤害时，你可以获得3点护甲，若如此做，当前回合结束时，你失去以此法获得的护甲。",
				["@kepingzhang"] = "屏障",
				["kepingzhangclear"] = "屏障",

			--引燃
			    ["keyinran"] = "引燃",
				[":keyinran"] = "限定技，出牌阶段，你可以令一名其他角色流失1点体力，且下两轮内，除濒死状态外，其回复的体力改为摸等量的牌。",
				["@keyinran"] = "引燃",
				["keyinranclear"] = "引燃",
				["beyinraned"] = "引燃重伤",
				["$keyinranlog"] = "%from 由于<font color='yellow'><b>重伤</b></font>，本次回复体力改为摸牌。",
				

			--虚弱
			    ["kexuruo"] = "虚弱",
				[":kexuruo"] = "限定技，出牌阶段，你可以选择一名其他角色，该角色的下两个摸牌阶段少摸一张牌且下一次造成的伤害-1。",
				["@kexuruo"] = "虚弱",
				["xuruodamage"] = "虚弱伤害",
				["xuruomopai"] = "虚弱摸牌",
				["xuruobuff"] = "虚弱",
				["$kexuruolog"] = "%from 由于<font color='yellow'><b>虚弱</b></font>导致本次伤害-1。",

			--强攻
				["keqianggong"] = "强攻",
				["@keqianggong"] = "强攻",
				[":keqianggong"] = "出牌阶段限一次，当你对一名其他角色造成伤害时，你可以令其直到你下回合开始，受到伤害后弃置一张牌。",
				["beqianggong"] = "强攻易损",
				["keqianggongqp"] = "强攻",
				["keqianggongclear"] = "强攻",
				
			--饼干配送
			    ["kebingganpeisong"] = "饼干",
				[":kebingganpeisong"] = "你的前四个准备阶段，你分别交替：1.摸一张牌；2.回复1点体力。\
				◆若你未受伤，回复体力改为摸一张牌。",
				["@binggan"] = "永续意志夹心饼干",
				["$bingganpeisong"] = "您的饼干已配送！",
				--["donebinggan"] = "配送完毕",
			
			--爆爆的小玩意
				["kepowderthing"] = "爆！",
				["@jinxthing"] = "猴子玩具",
				[":kepowderthing"] = "<font color='green'><b>每局限两次，</b></font>出牌阶段限一次，你可以对随机一名其他角色造成1点火焰伤害。\
				<font color='#FF6EC7'><b><i>“我，我一定能帮上忙！”</i></b></font>",

            --完美时机
			    ["kejinshenend"] = "秒表",
				["kejinshentarget"] = "秒表",
				["kejinshen"] = "秒表",
				["@kemiaobiao"] = "秒表",
				["jsstop"] = "秒表-凝滞",
				[":kejinshenend"] = "限定技，结束阶段，你可以摸一张牌并获得以下效果直到下回合开始：\
				1.其他角色使用牌不能指定你为目标；\
				2.防止你受到的伤害。",

			--微光
			    ["keshining"] = "微光",
				[":keshining"] = "出牌阶段限一次，你可以永久获得25%暴击几率，然后可以视为使用一张不计入次数的【杀】，若如此做，本回合结束阶段，你选择一项：失去1点体力上限，或失去1点体力并随机弃置一张手牌。",
				["keshiningda"] = "微光",
				["keshiningclear"] = "微光",
				["shining"] = "微光",
				["@shining"] = "微光",
				["shiningslash-ask"] = "请选择使用【杀】的目标",

				["weiguangxuanze"] = "微光",
				["weiguangxuanze:losehp"] = "失去1点体力并随机弃置一张牌",
				["weiguangxuanze:losemaxhp"] = "失去1点体力上限",

			--本索杂货铺
			    ["bensos"] = "杂货",
				["@kegold"] = "金币",
				[":bensos"] = "锁定技，每当你受到或造成1点伤害后，你获得1枚“金币”（至多10枚）；出牌阶段限一次，你可以移去金币执行对应效果或从牌堆获得牌。",
				["$kebensobuy"] = "交易愉快，欢迎下次光临！",
				["$kebensonomoney"] = "臭小子，没钱来这找打吗？",
				["$kebensonogood"] = "最近上城查得紧，没货了。",
				

				["goodsclass"] = "选择商品类别", 
				["goodsclass:basiccard"] = "-$基本牌$-",
				["goodsclass:equip"] = "-$装备牌$-",
				["goodsclass:effect"] = "-$特殊效果$-",
   
				["bensojibenpai"] = "选择基本牌",
				["bensojibenpai:slash"] = "-$ 杀 $-——4金",
				["bensojibenpai:jink"] = "-$ 闪 $-——4金",
				["bensojibenpai:wine"] = "-$ 酒 $-——6金",
				["bensojibenpai:peach"] = "-$ 桃 $-——6金",
   
				["bensozhuangbei"] = "选择装备牌",
				["bensozhuangbei:weapon"] = "-$ 武器牌 $-——5金",
				["bensozhuangbei:armor"] = "-$ 防具牌 $-——5金",
				["bensozhuangbei:addone"] = "-$ 防御坐骑 $-——5金",
				["bensozhuangbei:minusone"] = "-$ 进攻坐骑 $-——5金",
   
				["bensotexiao"] = "选择技能",
				["bensotexiao:judge"] = "-$ 废除判定区 $-——6金",
				["bensotexiao:maxhand"] = "-$ 手牌上限+1 $-——5金",
				["bensotexiao:msg"] = "-$ 打探消息 $-——2金",
				["bensosmsg-ask"] = "请选择观看手牌的角色",

			   -- ["bensotexiao:addone"] = "-$防御坐骑$-——3金",
				--["bensotexiao:minusone"] = "-$进攻坐骑$-——3金",
   
            --行窃预兆
			    ["kestole"] = "行窃",
				[":kestole"] = "锁定技，当你使用牌指定唯一其他角色为目标后，你有1/4的概率摸一张牌，若你没有摸牌，你有1/6的概率获得目标角色的一张牌。",
				["$kestoled"] = "%from 的行窃预兆被触发！",

			--警长的指控
			    ["kepolice"] = "指控",
				[":kepolice"] = "出牌阶段限一次，你可以弃置一张牌并选择一名体力值不小于你的其他角色，然后视为其对另一名你选择的角色<font color='red'><b>造成过</b></font>1点伤害。",
				["kepoliceCard"] = "警长的指控",
				["police-ask"] = "请选择受害者",

			--法力流系带
			    ["kefalidai"] = "法力",
				[":kefalidai"] = "出牌阶段限一次，当你失去牌后，若你的手牌数不大于1，你摸一张牌。\
				◆每触发两次，你的手牌上限永久+1。",
				["@kefalidai"] = "法力流系带",
				["$kefalidai"] = "%from 的法力流系带被触发！",

			--迅捷步法
			    ["kexunjiebufa"] = "步法",
				[":kexunjiebufa"] = "每当你使用或打出共计六张牌结算完毕后，你与其他角色的距离-1直到你使用下一张【杀】或伤害类普通锦囊牌指定目标后，你回复1点体力或摸一张牌。",
				["@kexunjiebufa"] = "迅捷步法",
				["$kexunjiebufa"] = "%from 的迅捷步法被触发！",
				["kexunjiebufajuli"] = "迅捷步法",
				["xunjiebufa-name"] = "迅捷步法",
				["xunjiebufa-name:huixue"] = "回复1点体力",
				["xunjiebufa-name:mopai"] = "摸一张牌",


			--无情猎手
			    ["kewuqinglieshou"] = "猎手",
				[":kewuqinglieshou"] = "锁定技，回合结束时，若你本回合出牌阶段没有造成过伤害，你与其他角色的距离-2直到下回合结束。",
				["$kewuqinglieshoulog"] = "%from 的无情猎手被触发！",

			--黑暗收割
			    ["kedarkshouge"] = "收割",
				[":kedarkshouge"] = "当你非因“黑暗收割”对一名其他角色造成伤害后，若其体力值不大于体力上限的一半，你可以弃置其一张牌并获得一枚“灵魂”，然后若你有至少三枚“灵魂”，你移去三枚“灵魂”并对其造成一点伤害。",
				["$kedarkshougelog"] = "%from 的黑暗收割被触发！",
				["@kedarkshouge"] = "黑暗收割",

			--守护者
			    ["keshouhuzhe"] = "守护",
				[":keshouhuzhe"] = "每轮开始时，你可以选择一名角色，直到下一轮开始，你与该角色共计受到：\
				○2点伤害后，每名角色摸一张牌；\
				○3点伤害后，每名角色获得1点护甲（下一轮开始时，移去这些护甲）。",
				["$keshouhuzhe"] = "%from 的守护者被触发！",
				["@keshouhuzhe"] = "守护者",
				["keshouhuzhe-ask"] = "请选择“守护者”保护的目标",
				["keshouhuzhebaohu"] = "守护者保护",
				["$keshouhuzhelogmopai"] = "%from 的守护者被触发！摸一张牌！",
				["$keshouhuzhelog"] = "%from 的守护者被触发！获得护甲！",
   
			   
		--翻译
		        ["$kedianxing1"] = "（电刑）",
				["$kedianxing2"] = "（电刑）",
				["$kedianxing3"] = "（电刑）",
				["$kezhimingjiezou1"] = "（致命节奏）",
				["$kezhimingjiezou2"] = "（致命节奏）",
				["$kepowderthing1"] = "（爆！）",
				["$keshining1"] = "（使用微光）",
				["$keqianggong1"] = "（强攻）",
				["$keqianggong2"] = "（强攻）",
				["$kezhiliaoshu1"] = "（治疗术）",
				["$kezhiliaoshu2"] = "（治疗术）",
				["$kezhiliaoshu3"] = "（治疗术）",
				["$keshanxian1"] = "（闪现）",
				["$keshanxian2"] = "（闪现）",
				["$kepingzhang1"] = "（屏障）",
				["$kepingzhang2"] = "（屏障）",
				["$keyinran1"] = "（引燃）",
				["$keyinran2"] = "（引燃）",
				["$keyinran3"] = "（引燃）",
				["$keyinran4"] = "（引燃）",
				["$keyinran5"] = "（火焰燃烧声）",
				["$kexuruo1"] = "（虚弱）",
				["$kexuruo2"] = "（虚弱）",
				["$bensos1"] = "（金币声）",
				["$bensos2"] = "（金币声）",
				["$kestole1"] = "（金币迸发）",
				["$kestole2"] = "（金币迸发）",
				["$kepolice1"] = "（皮尔特沃夫的警报）",
				["$kedarkshouge1"] = "（黑暗收割）",
				["$kedarkshouge2"] = "（获得灵魂）",
				["$kexunjiebufa1"] = "（迅捷步法）",
				["$keshouhuzhe1"] = "（守护者）",
				["$kejinshenend1"] = "（秒表）",
			
}
return {extension}

