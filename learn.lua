-- ==================== 包和全局技能列表定义 ====================
extension = sgs.Package("learn")
-- 创建一个列表，用于存放所有需要全局注册的辅助技能
local skills = sgs.SkillList()
-- ==========================================================

-- ==================== 武将：米关羽 ====================
new_simayi = sgs.General(extension, "new_simayi", "god", 3)

tianmu = sgs.CreateTriggerSkill{
    name = "tianmu",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.Damaged},
    on_trigger = function(self,event,player,data)
        player:drawCards(1, self:objectName())
        local damage = data:toDamage()
        local from = damage.from
        local room = player:getRoom()
        if from and from:isAlive() and from:objectName() ~= player:objectName() then
            if room:askForSkillInvoke(player, self:objectName(), sgs.QVariant.fromValue(from)) then
                local damage2 = sgs.DamageStruct()
                damage2.from = player
                damage2.to = from
                damage2.damage = damage.damage
                damage2.nature = sgs.DamageStruct_Fire
                room:damage(damage2)
            end
        end
    end
}

nitian = sgs.CreateTriggerSkill{
    name = "nitian",
    events = {sgs.EventPhaseStart, sgs.CardUsed},
    frequency = sgs.Skill_Compulsory,
    on_trigger = function(self,event,player,data)
        local room = player:getRoom()
        if (event == sgs.CardUsed) and (player:getPhase() == sgs.Player_Play) then
            local use = data:toCardUse()
            if use.card:isKindOf("Slash") then
                room:setPlayerFlag(player, "-SlashUsed") -- 重置杀的次数限制
                if (use.card:getSuit() == sgs.Card_Club) or (use.card:getSuit() == sgs.Card_Spade) then
                    player:drawCards(1, self:objectName())
                end
            end
        end
        if(event == sgs.EventPhaseStart and player:getPhase()==sgs.Player_Start) then
			if player:getHp() > 1 then
				room:loseHp(player,1)
			end
            local recover = sgs.RecoverStruct()
            recover.who = player
            recover.recover = 1
            room:recover(player,recover)
            player:drawCards(1 + player:getLostHp(), self:objectName())
        end
    end
}

nitianfushu_dist = sgs.CreateDistanceSkill{
    name = "#nitianfushu-dist",
    global = true,
    correct_func = function(self, from, to)
        if from and from:hasSkill("nitianfushu2") then
            return -1
        end
        if to and to:hasSkill("nitianfushu2") then
            return to:getLostHp() + 1
        end
        return 0
    end
}
if not sgs.Sanguosha:getSkill("#nitianfushu-dist") then skills:append(nitianfushu_dist) end

nitianfushu2 = sgs.CreateViewAsSkill {
    name = "nitianfushu2",
    n = 1,
    view_filter = function(self, cards, to_select)
        return to_select:isRed()
    end,
    view_as = function(self, cards)
        if #cards ~= 1 then return nil end
        local slash = sgs.Sanguosha:cloneCard("slash", cards[1]:getSuit(), cards[1]:getNumber())
        slash:addSubcard(cards[1])
		slash:setSkillName(self:objectName())
        return slash
    end,
    enabled_at_play = function(self, player)
        return true
    end,
}

new_simayi:addSkill(tianmu)
new_simayi:addSkill(nitian)
new_simayi:addSkill(nitianfushu2)
-- ========================================================


-- ==================== 武将：老米 ====================
mijunsheng = sgs.General(extension, "mijunsheng", "god", 1)

shenpanCard = sgs.CreateSkillCard{
	name = "shenpanCard",
	target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select)
		if #targets ~= 0 then return false end
		return to_select and to_select:objectName() ~= sgs.Self:objectName() and to_select:getMark("@shenpan") == 0
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:addPlayerMark(target, "@shenpan")
		room:setPlayerFlag(player, "usedshenpanCard")
	end
}
shenpan_view = sgs.CreateViewAsSkill {
	name = "shenpan_view",
	n = 1,
	view_filter = function(self, cards, to_select)
		return not (to_select:isKindOf("TrickCard") or to_select:isKindOf("DelayedTrick"))
	end,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local card = shenpanCard:clone()
		card:addSubcard(cards[1])
        card:setSkillName(self:objectName())
		return card
	end,
	enabled_at_play = function(self, player)
		return not player:hasFlag("usedshenpanCard")
	end,
}
shenpan = sgs.CreateTriggerSkill{
    name = "shenpan_view",
	view_as_skill = shenpan_view,
	events = {sgs.EventPhaseChanging},
	can_trigger = function(self, player)
		return player and player:getMark("@shenpan") > 0
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_RoundStart then
			room:sendCompulsoryTriggerLog(player, self:objectName())
			local judge = sgs.JudgeStruct()
			judge.pattern = "."
			judge.good = true
			judge.play_animation = false
			judge.who = player
			judge.reason = self:objectName()
			room:judge(judge)
			if judge.card:getSuit() ~= sgs.Card_Diamond then
				local mijunsheng_player = room:findPlayerBySkillName(self:objectName())
				if mijunsheng_player then
					room:logSkillInvoke(self:objectName(), mijunsheng_player)
					local damage = sgs.DamageStruct()
					damage.from = mijunsheng_player
					damage.to = player
					damage.damage = (player:getHp() > 1) and (player:getHp() - 1) or player:getMaxHp()
					damage.nature = sgs.DamageStruct_Thunder
					if damage.damage > 0 then room:damage(damage) end
				end
			end
			room:setPlayerMark(player, "@shenpan", 0)
		end
		return false
	end,
}
mijunsheng:addSkill(shenpan)

zegui = sgs.CreateTriggerSkill{
    name = "zegui",
    events = {sgs.CardUsed},
    frequency = sgs.Skill_Frequent,
    on_trigger = function(self, event, player, data)
        if player:getPhase() == sgs.Player_Play then
            player:getRoom():drawCards(player, 1, self:objectName())
        end
        return false
    end
}
zegui_maxcards = sgs.CreateMaxCardsSkill{
    name = "#zegui-maxcards",
    global = true,
    extra_func = function(self, target)
        if target and target:hasSkill("zegui") then
            return 3
        end
        return 0
    end
}
if not sgs.Sanguosha:getSkill("#zegui-maxcards") then skills:append(zegui_maxcards) end
mijunsheng:addSkill(zegui)
mijunsheng:addSkill("juejing")

-- ==================== 狂刀技能最终修复版 ====================
kuangdao = sgs.CreateTriggerSkill{
    name = "kuangdao",
    -- [!!] 核心BUG修复：使用 ConfirmingTarget 事件，逻辑最清晰
    events = {sgs.ConfirmingTarget},
    on_trigger = function(self, event, player, data)
        -- 在这个事件里:
        -- player -> 是【杀】的目标
        -- use.from -> 是【杀】的使用者
        
        local room = player:getRoom()
        local use = data:toCardUse()
        local user = use.from -- 【杀】的使用者

        -- 检查【杀】的使用者是否拥有"狂刀"技能，并且卡牌是【杀】
        if user and user:hasSkill(self:objectName()) and use.card:isKindOf("Slash") then
            -- 由【杀】的使用者(user)来选择是否对当前目标(player)发动技能
            if room:askForSkillInvoke(user, self:objectName(), sgs.QVariant.fromValue(player)) then
                room:notifySkillInvoked(user, self:objectName())
                
                -- 令目标(player)的技能失效
                room:addPlayerMark(player, "@KuangDaoDisabled")
                local log = sgs.LogMessage()
                log.type = "#SkillInvalid"
                log.from = user
                log.to:append(player)
                log.arg = self:objectName()
                room:sendLog(log)
                
                -- 进行判定，由技能发动者(user)来执行
                local judge = sgs.JudgeStruct()
                judge.pattern = "."
                judge.good = true
                judge.reason = self:objectName()
                judge.who = user
                room:judge(judge)
                
                local judge_card = judge.card
                local suit_str = judge_card:getSuitString()
                local pattern = "..:" .. suit_str .. ":."
                
                -- 要求目标(player)弃牌
                local to_discard = room:askForCard(player, pattern, "@kuangdao-discard:" .. suit_str, sgs.QVariant(), sgs.Card_MethodDiscard, user, false)
                
                if not to_discard then
                    -- 如果目标未能弃置，施加惩罚
                    player:setTag("KuangDao_NoJink", sgs.QVariant(true))
                    player:setTag("KuangDao_DamagePlus", sgs.QVariant(true))
                    local log_punish = sgs.LogMessage()
                    log_punish.type = "#KuangDaoPunish"
                    log_punish.from = user
                    log_punish.to:append(player)
                    log_punish.arg = self:objectName()
                    room:sendLog(log_punish)
                end
            end
        end
        return false
    end
}
mijunsheng:addSkill(kuangdao)
-- ========================================================

kuangdao_prohibit = sgs.CreateProhibitSkill{
    name = "#kuangdao-prohibit",
    global = true,
    is_prohibited = function(self, from, to, skill)
        if to and to:getMark("@KuangDaoDisabled") > 0 and not skill:isCompulsorySkill() then
            return true
        end
        return false
    end
}
if not sgs.Sanguosha:getSkill("#kuangdao-prohibit") then skills:append(kuangdao_prohibit) end

kuangdao_noshan = sgs.CreateFilterSkill{
    name = "#kuangdao-noshan",
    global = true,
    view_filter = function(self, to_select)
        if to_select:isKindOf("Jink") and sgs.Self:getTag("KuangDao_NoJink"):toBool() then
            return true
        end
        return false
    end,
    view_as = function(self, card)
        return nil
    end
}
if not sgs.Sanguosha:getSkill("#kuangdao-noshan") then skills:append(kuangdao_noshan) end

kuangdao_damage = sgs.CreateTriggerSkill{
    name = "#kuangdao-damage",
    global = true,
    on_trigger = function(self, event, player, data)
        local damage = data:toDamage()
        if damage.to and damage.to:getTag("KuangDao_DamagePlus"):toBool() and damage.card and damage.card:isKindOf("Slash") then
            local room = damage.to:getRoom()
            local log = sgs.LogMessage()
            log.type = "#KuangDaoDamage"
            log.from = damage.from
            log.to:append(damage.to)
            log.arg = "kuangdao"
            log.arg2 = sgs.QVariant(damage.damage + 1):toString()
            room:sendLog(log)
            damage.damage = damage.damage + 1
            data:setValue(damage)
        end
        return false
    end
}
if not sgs.Sanguosha:getSkill("#kuangdao-damage") then skills:append(kuangdao_damage) end

kuangdao_clear = sgs.CreateTriggerSkill{
    name = "#kuangdao-clear",
    events = {sgs.SlashEffected, sgs.EventPhaseChanging},
    global = true,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.SlashEffected then
            local effect = data:toSlashEffect()
            if effect.to:getTag("KuangDao_NoJink"):toBool() or effect.to:getTag("KuangDao_DamagePlus"):toBool() then
                effect.to:removeTag("KuangDao_NoJink")
                effect.to:removeTag("KuangDao_DamagePlus")
            end
        elseif event == sgs.EventPhaseChanging then
            local change = data:toPhaseChange()
            if change.to == sgs.Player_NotActive then
                for _, p in sgs.qlist(room:getAlivePlayers()) do
                    if p:getMark("@KuangDaoDisabled") > 0 then
                        room:removePlayerMark(p, "@KuangDaoDisabled", p:getMark("@KuangDaoDisabled"))
                    end
                end
            end
        end
        return false
    end
}
if not sgs.Sanguosha:getSkill("#kuangdao-clear") then skills:append(kuangdao_clear) end
-- ========================================================


-- ==================== 翻译表 ====================
sgs.LoadTranslationTable{
    ["learn"]="俊生包",
    ["new_simayi"]="米关羽",
    ["#new_simayi"]="仁慈之武圣",
    ["designer:new_simayi"]="米俊生",
    ["cv:new_simayi"]="米俊生",
    ["illustrator:new_simayi"]="Online",
    ["tianmu"]="天幕",
    [":tianmu"]="<font color=red><b>锁定技，</b></font>当你受到伤害后，摸一张牌；然后你可以对伤害来源造成等量的火焰伤害。",
    ["nitian"]="逆天",
    [":nitian"]="<font color=red><b>锁定技，</b></font>回合开始时，若你的体力值不为1，你失去1点体力，然后你回复1点体力并摸X+1张牌（X为你已损失的体力值）。你使用黑色【杀】后摸一张牌；你使用【杀】的次数不受限制。",
    ["nitianfushu2"]="臻武",
    [":nitianfushu2"]="你可以将红色牌当【杀】使用。你计算与其他角色的距离-1；其他角色计算与你的距离+X+1（X为你已损失的体力值）。",

    ["mijunsheng"]="老米",
    ["#mijunsheng"]="交兵",
    ["designer:mijunsheng"]="米俊生",
    ["cv:mijunsheng"]="米俊生",
    ["illustrator:mijunsheng"]="Online",
    ["shenpan_view"]="神判",
    [":shenpan_view"]="出牌阶段限一次，你可以弃置一张非锦囊牌并选择一名其他角色，令其获得“神罚”标记。有“神罚”标记的角色的回合开始时，进行一次判定，若结果不为♦，你对其造成伤害，直到其体力值为1（若其体力值已为1，则改为对其造成其体力上限的伤害）。",
    ["@shenpan"]="神罚",
	["shenpan"]="神判",
	["$shenpan_view"] = "<font color='yellow'><b>天降神威</b></font> ！",
	["zegui"]="则规",
	[":zegui"]="出牌阶段，你每使用一张牌，便摸一张牌。你的手牌上限额外+3。",
    ["juejing"]="绝境",
    [":juejing"]="锁定技，摸牌阶段，你摸牌的数量为你已损失的体力值+2。",
	["kuangdao"]="狂刀",
    [":kuangdao"]="当你使用【杀】指定一名目标后，你可令其本回合非锁定技失效。然后你进行一次判定，除非该角色弃置一张与判定结果花色相同的牌，否则此【杀】对其结算时，其不能使用【闪】且此【杀】伤害+1。",
    ["@kuangdao-discard"]="请弃置一张<font color=#DC143C><b>%arg</b></font>花色的牌，否则【杀】将不可闪避且伤害+1",
    ["#SkillInvalid"] = "%from发动【%arg】，%to的非锁定技于本回合内失效",
    ["#KuangDaoPunish"] = "%to未能打出对应花色的牌，【狂刀】的惩罚效果生效",
    ["#KuangDaoDamage"] = "【%arg】效果使伤害增加，最终为%arg2点"
}
-- =================================================


-- ==================== 注册所有全局技能 ====================
sgs.Sanguosha:addSkills(skills)
-- ====================================================

return {extension}