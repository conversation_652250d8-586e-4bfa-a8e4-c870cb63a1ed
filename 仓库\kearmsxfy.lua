--==《新武将》==--
extension_li = sgs.Package("kearmsxfyli", sgs.Package_GeneralPack)
extension_zhen = sgs.Package("kearmsxfyzhen", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

function KeToData(self)
	local data = sgs.QVariant()
	if type(self)=="string"
	or type(self)=="boolean"
	or type(self)=="number"
	then data = sgs.QVariant(self)
	elseif self~=nil then data:setValue(self) end
	return data
end

function kegetCardList(intlist)
	local ids = sgs.CardList()
	for _, id in sgs.qlist(intlist) do
		ids:append(sgs.Sanguosha:getCard(id))
	end
	return ids
end

--buff集中
kearmsxfyslashmore = sgs.CreateTargetModSkill{
	name = "kearmsxfyslashmore",
	pattern = ".",
	residue_func = function(self, from, card, to)
		local n = 0
		--[[if card:getSkillName()=="kelqjuesui" and from:hasSkill("kelqjuesuiUse") then
			n = n + 1000
		end]]
		return n
	end,
	extra_target_func = function(self, from, card)
		local n = 0
		--[[if from:hasSkill("keshuaiguanshi")
		and card:isKindOf("FireAttack") 
		and (card:getSkillName() == "keshuaiguanshi") then
			n = n + 1000
		end]]
		return n
	end,
	distance_limit_func = function(self, from, card, to)
		local n = 0
		--[[if card:isKindOf("Slash") and card:getSkillName() == "_keshuaiyansha" then
			n = n + 1000
		end]]
		return n
	end
}
if not sgs.Sanguosha:getSkill("kearmsxfyslashmore") then skills:append(kearmsxfyslashmore) end

sgs.LoadTranslationTable{
    ["kearmsxfyli"] = "四象封印·离",
	["kearmsxfyzhen"] = "四象封印·震",
}

kesxdengzhi = sgs.General(extension_li, "kesxdengzhi", "shu", 3)

kesxjimeng = sgs.CreateTriggerSkill{
	name = "kesxjimeng",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) 
		and (player:getCardCount() > 0)
		and (player:getPhase() == sgs.Player_Start) then
			local fri = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "kesxjimeng-ask",true,true)
			if fri then
				local mycards = room:askForExchange(player, self:objectName(), 99, 1, true, "kesxjimeng-choose:"..fri:objectName(),false)
				room:obtainCard(fri, mycards)
				local hiscards = room:askForExchange(fri, self:objectName(), 99, 1, true, "kesxjimeng-choose:"..player:objectName(),false)
				room:obtainCard(player, hiscards)
			end
		end	
	end,
	--[[can_trigger = function(self, player)
		return true
	end]]
}
kesxdengzhi:addSkill(kesxjimeng)

kesxhehe = sgs.CreateTriggerSkill{
	name = "kesxhehe",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseEnd) and (player:getPhase() == sgs.Player_Draw) then
			local players = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if (p:getHandcardNum() == player:getHandcardNum()) then
					players:append(p)
				end
			end
			local fris = room:askForPlayersChosen(player, players, self:objectName(), 0, 2, "kesxhehe-ask", true, true)
			if fris:length() > 0 then
			    for _, q in sgs.qlist(fris) do
					q:drawCards(1,self:objectName())
				end
			end
		end	
	end,
	--[[can_trigger = function(self, player)
		return true
	end]]
}
kesxdengzhi:addSkill(kesxhehe)


sgs.LoadTranslationTable{

	["kesxdengzhi"] = "邓芝[离]", 
	["&kesxdengzhi"] = "邓芝",
	["#kesxdengzhi"] = "绝境的外交家",
	["designer:kesxdengzhi"] = "官方",
	["cv:kesxdengzhi"] = "官方",
	["illustrator:kesxdengzhi"] = "凝聚永恒",

	["kesxjimeng"] = "急盟",
	["kesxjimeng-ask"] = "你可以发动“急盟”交给一名角色任意张牌",
	["kesxjimeng-choose"] = "急盟：请选择交给 %src 的牌",
	[":kesxjimeng"] = "准备阶段，你可以交给一名其他角色至少一张牌，然后其交给你至少一张牌。",

	["kesxhehe"] = "和合",
	["kesxhehe-ask"] = "你可以发动“和合”令至多两名角色各摸一张牌",
	[":kesxhehe"] = "摸牌阶段结束时，你可以令至多两名手牌数与你相同的其他角色各摸一张牌。",

	["$kesxjimeng1"] = "精诚协作，以御北虏。",
	["$kesxjimeng2"] = "两家携手，共抗时艰。",
	["$kesxhehe1"] = "清廉严谨，以身作则。",
	["$kesxhehe2"] = "赏罚明断，自我而始。",

	["~kesxdengzhi"] = "大王命世之英，何行此不智之举？",
}

kesxwenyang = sgs.General(extension_li, "kesxwenyang", "wei", 4)

kesxquedi = sgs.CreateOneCardViewAsSkill{
	name = "kesxquedi",
	response_or_use = true,
	view_filter = function(self, card)
		if not card:isKindOf("Slash") then return false end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local juedou = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
			juedou:addSubcard(card:getEffectiveId())
			juedou:deleteLater()
			return juedou:isAvailable(sgs.Self)
		end
		return true
	end,
	view_as = function(self, card)
		local juedou = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
		juedou:addSubcard(card:getId())
		juedou:setSkillName("_kesxquedi")
		return juedou
	end,
	enabled_at_play = function(self, player)
		return true
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "duel"
	end
}
kesxwenyang:addSkill(kesxquedi)

sgs.LoadTranslationTable{

	["kesxwenyang"] = "文鸯[离]", 
	["&kesxwenyang"] = "文鸯",
	["#kesxwenyang"] = "独骑破军",
	["designer:kesxwenyang"] = "官方",
	["cv:kesxwenyang"] = "官方",
	["illustrator:kesxwenyang"] = "鬼画府",

	["kesxquedi"] = "却敌",
	[":kesxquedi"] = "你可以将一张【杀】当【决斗】使用。",

	["$kesxquedi1"] = "哼，缘何退却？有胆来战！",
	["$kesxquedi2"] = "八千之众，尚不如我一人乎？",

	["~kesxwenyang"] = "得报父仇，我无憾矣。",
}

kesxchengpu = sgs.General(extension_li, "kesxchengpu", "wu", 4)

kesxchunlao = sgs.CreateTriggerSkill{
    name = "kesxchunlao",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseEnd,sgs.CardsMoveOneTime},
	--[[can_trigger = function(self, target)
		return target and target:isAlive()
	end,]]
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from and player:objectName() == move.from:objectName()
			and bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)==sgs.CardMoveReason_S_REASON_DISCARD
			and (player:getPhase() == sgs.Player_Discard)
			and player:hasSkill(self:objectName()) then
				local tag = player:getTag("kesxchunlaoToGet"):toIntList()
				for _,card_id in sgs.qlist(move.card_ids) do
					tag:append(card_id)
				end
				local d = sgs.QVariant()
				d:setValue(tag)
				player:setTag("kesxchunlaoToGet", d)
			end
		end
		if (event == sgs.EventPhaseEnd) then
			if (player:getPhase() == sgs.Player_Discard) then
				local tag = player:getTag("kesxchunlaoToGet"):toIntList()
				if (tag:length() >= 2) then
					local eny = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "kesxchunlao-ask",true,true)
					if eny then
						room:broadcastSkillInvoke(self:objectName())
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcards(kegetCardList(eny:handCards()))
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RECAST, eny:objectName(), self:objectName(), "")
						room:moveCardTo(dummy, nil, sgs.Player_DiscardPile, reason)
						local dummyt = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummyt:addSubcards(kegetCardList(tag))
						eny:obtainCard(dummyt)
						dummy:deleteLater()
						dummyt:deleteLater()
						local to_data = sgs.QVariant()
						to_data:setValue(player)
						if eny:askForSkillInvoke(self:objectName(), KeToData("kesxchunlao0:"..player:objectName()), false) then
							room:recover(player, sgs.RecoverStruct())
						end
					end
				end
				player:removeTag("kesxchunlaoToGet")
			end
		end
	end
}
kesxchengpu:addSkill(kesxchunlao)

sgs.LoadTranslationTable{

	["kesxchengpu"] = "程普[离]", 
	["&kesxchengpu"] = "程普",
	["#kesxchengpu"] = "三朝虎臣",
	["designer:kesxchengpu"] = "官方",
	["cv:kesxchengpu"] = "官方",
	["illustrator:kesxchengpu"] = "玖等仁品",

	["kesxchunlao"] = "醇醪",
	["kesxchunlao:kesxchunlao0"] = "醇醪：你可以令 %src 回复1点体力",
	["kesxchunlao-ask"] = "你可以发动“醇醪”将弃置的牌交换一名其他角色的手牌",
	[":kesxchunlao"] = "弃牌阶段结束时，若你本阶段弃置了至少两张牌，你可以令一名其他角色将所有手牌置入弃牌堆并获得你弃置的牌，然后其可以令你回复1点体力。",

	["$kesxchunlao1"] = "醇酒佳酿杯中饮，醉酒提壶力千钧。",
	["$kesxchunlao2"] = "身被疮痍，唯酒能医。",

	["~kesxchengpu"] = "酒尽身死，壮哉！",
}

kesxlijue = sgs.General(extension_li, "kesxlijue", "qun", 5)

kesxxiongsuan = sgs.CreateTriggerSkill{
    name = "kesxxiongsuan",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
	    local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and (player:getPhase() == sgs.Player_Start) then
			local ifwudayu = 1
			for _, p in sgs.qlist(room:getAllPlayers()) do 
				if (p:getHp() > player:getHp()) then
					ifwudayu = 0
					break
				end
			end
			if ifwudayu == 1 then
				local players = sgs.SPlayerList()
				for _, q in sgs.qlist(room:getAllPlayers()) do 
					if q:getHp() == player:getHp() then
						players:append(q)
					end
				end
				local fris = room:askForPlayersChosen(player, players, self:objectName(), 1, 99, "kesxxiongsuan-ask", true, true)
				room:broadcastSkillInvoke(self:objectName())
				for _, q in sgs.qlist(fris) do 
					room:damage(sgs.DamageStruct(self:objectName(), player, q))
				end
			end
		end
	end
}
kesxlijue:addSkill(kesxxiongsuan)

sgs.LoadTranslationTable{

	["kesxlijue"] = "李傕[离]", 
	["&kesxlijue"] = "李傕",
	["#kesxlijue"] = "奸谋恶勇",
	["designer:kesxlijue"] = "官方",
	["cv:kesxlijue"] = "官方",
	["illustrator:kesxlijue"] = "凝聚永恒",

	["kesxxiongsuan"] = "兇算",
	["kesxxiongsuan-ask"] = "请选择发动“兇算”造成伤害的角色",
	[":kesxxiongsuan"] = "锁定技，准备阶段，若没有角色体力值大于你，你对至少一名体力值等于你的角色各造成1点伤害。",

	["$kesxxiongsuan1"] = "狼抗傲慢，祸福沿袭！",
	["$kesxxiongsuan2"] = "我就喜欢听这，狼嚎悲鸣！",

	["~kesxlijue"] = "这一次我拿不下长安了吗？",
}

kesxfeiyi = sgs.General(extension_li, "kesxfeiyi", "shu", 3)

kesxtiaoheCard = sgs.CreateSkillCard{
	name = "kesxtiaoheCard",
	target_fixed = false,
	will_throw = false,
	skill_name = "_keshuaijinglei",
	filter = function(self, targets, to_select, player)
		if ((#targets == 0) and (to_select:getWeapon() == nil)) then
			return false
		end
		if ((#targets == 1) and (to_select:getArmor() == nil)) then
			return false
		end
		return #targets < 2
	end,
	feasible = function(self, targets)
		return #targets == 2
	end ,
	on_use = function(self, room, player, targets)
		room:throwCard(targets[1]:getWeapon(), targets[1], player)
		room:throwCard(targets[2]:getArmor(), targets[2], player)
	end
}

kesxtiaohe = sgs.CreateViewAsSkill{
	name = "kesxtiaohe",
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return false
	end ,
	view_as = function(self, cards)
		return kesxtiaoheCard:clone()
	end ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#kesxtiaoheCard") 
	end
}
kesxfeiyi:addSkill(kesxtiaohe)

kesxqiansu = sgs.CreateTriggerSkill{
	name = "kesxqiansu",
	frequency = sgs.Skill_Frequent,
	events = {sgs.TargetSpecified},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetSpecified then
			local use = data:toCardUse()
			if use.card:isKindOf("TrickCard") and (player:getEquipsId():length() == 0) then
				if room:askForSkillInvoke(player, self:objectName(), data) then 
					player:drawCards(1,self:objectName())
				end
			end
		end
	end,
}
kesxfeiyi:addSkill(kesxqiansu)

sgs.LoadTranslationTable{

	["kesxfeiyi"] = "费祎[离]", 
	["&kesxfeiyi"] = "费祎",
	["#kesxfeiyi"] = "洞世权相",
	["designer:kesxfeiyi"] = "官方",
	["cv:kesxfeiyi"] = "官方",
	["illustrator:kesxfeiyi"] = "凝聚永恒",

	["kesxtiaohe"] = "调和",
	[":kesxtiaohe"] = "出牌阶段限一次，你可以选择一名装备区有武器牌的角色和另一名装备区有防具牌的角色，然后你分别弃置这两张牌。",

	["kesxqiansu"] = "谦素",
	[":kesxqiansu"] = "当你成为锦囊牌的目标后，若你的装备区没有牌，你可以摸一张牌。",

	["$kesxtiaohe1"] = "斟酌损益，进尽忠言，此臣等之任也。",
	["$kesxtiaohe2"] = "两相匡护，以各安其分，兼尽其用。",
	["$kesxqiansu1"] = "承葛公遗托，富国安民。",
	["$kesxqiansu2"] = "保国治民，敬守社稷。",

	["~kesxfeiyi"] = "吾何惜一死，惜不见大汉中兴矣。",
}

kesxfanyufeng = sgs.General(extension_li, "kesxfanyufeng", "qun", 3,false)

kesxbazhanCard = sgs.CreateSkillCard{
	name = "kesxbazhanCard",
	target_fixed = false,
	will_throw = false,
	skill_name = "_kesxbazhan",
	filter = function(self, targets, to_select, player)
		return (#targets < 1) and to_select:isMale()
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:showCard(player,self:getSubcards():first())
		room:obtainCard(target, self)
		local cc = sgs.Sanguosha:getCard(self:getSubcards():first())
		local cardtype = cc:getType()
		local xxx = room:askForExchange(target, "kesxbazhan", 1, 1, false, "kesxbazhan-choose:"..player:objectName(), true, "^"..cardtype) 
		if xxx:getSubcards():length() > 0 then
			room:showCard(target,xxx:getSubcards():first())
		    room:obtainCard(player, xxx)
		end
	end
}

kesxbazhan = sgs.CreateViewAsSkill{
	name = "kesxbazhan",
	n = 1 ,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end ,
	view_as = function(self, cards)
		if #cards == 1 then
			local card = kesxbazhanCard:clone()
			card:addSubcard(cards[1])
			return card
		else 
			return nil
		end
	end ,
	enabled_at_play = function(self, player)
		return player:usedTimes("#kesxbazhanCard") < 2
	end
}
kesxfanyufeng:addSkill(kesxbazhan)

kesxqiaoyingex = sgs.CreateCardLimitSkill{
	name = "#kesxqiaoyingex",
	limit_list = function(self, player)
		if (player:getMark("&kesxqiaoying-Clear") < player:getHandcardNum())
		and (player:getMark("kesxqiaoyingeffect-Clear") > 0) then
			return "use"
		else
			return ""
		end
	end,
	limit_pattern = function(self, player)
		if (player:getMark("&kesxqiaoying-Clear") < player:getHandcardNum())
		and (player:getMark("kesxqiaoyingeffect-Clear") > 0) then
			return ".|red|.|hand"
		else
			return ""
		end
	end
}
kesxfanyufeng:addSkill(kesxqiaoyingex)

kesxqiaoying = sgs.CreateTriggerSkill{
	name = "kesxqiaoying",
	events = {sgs.EventPhaseStart,sgs.DamageInflicted,sgs.GameStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if (event == sgs.GameStart) then
			if not player:hasSkill("kesxqiaoyingex") then
				room:handleAcquireDetachSkills(player, "kesxqiaoyingex")
			end
		end
		if (event == sgs.DamageInflicted) then
			local damage = data:toDamage()
			if room:getCurrent():hasSkill("kesxqiaoying") 
			and (damage.to:getHandcardNum() > damage.to:getMark("&kesxqiaoying-Clear")) then
				local hurt = damage.damage
				damage.damage = 1 + hurt
				data:setValue(damage)
			end
		end
		if (event == sgs.EventPhaseStart) and player:hasSkill(self:objectName())
		and (player:getPhase() == sgs.Player_RoundStart) then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if not p:hasSkill("kesxqiaoyingex") then
				    room:handleAcquireDetachSkills(p, "kesxqiaoyingex")
				end
				room:setPlayerMark(p,"&kesxqiaoying-Clear",p:getHandcardNum())
				room:setPlayerMark(p,"kesxqiaoyingeffect-Clear",1)
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
}
kesxfanyufeng:addSkill(kesxqiaoying)
--extension_li:insertRelatedSkills("kesxqiaoying", "#kesxqiaoyingex")

sgs.LoadTranslationTable{

	["kesxfanyufeng"] = "樊玉凤[离]", 
	["&kesxfanyufeng"] = "樊玉凤",
	["#kesxfanyufeng"] = "红鸾寡宿",
	["designer:kesxfanyufeng"] = "官方",
	["cv:kesxfanyufeng"] = "官方",
	["illustrator:kesxfanyufeng"] = "琬焱",

	["kesxbazhan"] = "把盏",
	["kesxbazhan-choose"] = "你可以交给 %src 一张牌",
	
	[":kesxbazhan"] = "出牌阶段限两次，你可以展示一张手牌并交给一名男性角色，然后其可以展示一张与此牌类别不同的手牌并交给你。",

	["kesxqiaoying"] = "醮影",
	[":kesxqiaoying"] = "在你的回合内，手牌数大于其当前回合开始时的手牌数的角色不能使用红色手牌且其受到的伤害+1。",

	["$kesxbazhan1"] = "今与将军把盏，酒不醉人人自醉。",
	["$kesxbazhan2"] = "昨日把盏消残酒，醉时朦胧见君来。",
	["$kesxqiaoying1"] = "经年相别，顾盼云泥，此间再未合影。",
	["$kesxqiaoying2"] = "举杯邀月盼云郎，我与月影成两人。",

	["~kesxfanyufeng"] = "浓酒只消昨日恨，奈何岁月败美人。 ",
}

kesxchengyu = sgs.General(extension_li, "kesxchengyu", "wei", 3,true)

kesxchengyu:addSkill("shefu")

kesxyibing = sgs.CreateTriggerSkill{
	name = "kesxyibing",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.AskForPeaches},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.AskForPeaches) 
		and player:hasSkill(self:objectName()) then
			local dying_data = data:toDying()
			local source = dying_data.who
			if (source:objectName() ~= player:objectName()) and not source:isKongcheng() then
				if player:askForSkillInvoke(self:objectName(), data) then
					room:broadcastSkillInvoke(self:objectName())
					local card_id = room:askForCardChosen(player, source, "h", self:objectName())
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
					room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
				end
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
kesxchengyu:addSkill(kesxyibing)

sgs.LoadTranslationTable{

	["kesxchengyu"] = "程昱[离]", 
	["&kesxchengyu"] = "程昱",
	["#kesxchengyu"] = "泰山捧日",
	["designer:kesxchengyu"] = "官方",
	["cv:kesxchengyu"] = "官方",
	["illustrator:kesxchengyu"] = "DH",

	["kesxyibing"] = "益兵",
	[":kesxyibing"] = "当其他角色进入濒死状态时，你可以获得其一张牌。",

	["$kesxyibing1"] = "助曹公者昌，逆曹公者亡！",
	["$kesxyibing2"] = "愚民不可共济大事，必当与智者为伍。",

	["~kesxchengyu"] = "此诚报效国家之时，吾却休矣。",
}

kesxzhangyi = sgs.General(extension_li, "kesxzhangyi", "shu", 4,true)

kesxzhiyiCard = sgs.CreateSkillCard{
	name = "kesxzhiyiCard" ,
	mute = true,
	filter = function(self, targets, to_select)
		local targets_list = sgs.PlayerList()
		for _, target in ipairs(targets) do
			targets_list:append(target)
		end
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slash:setSkillName("kesxzhiyi")
		slash:deleteLater()
		return slash:targetFilter(targets_list, to_select, sgs.Self)
	end ,
	on_use = function(self, room, source, targets)
		local targets_list = sgs.SPlayerList()
		for _, target in ipairs(targets) do
			if source:canSlash(target, nil, false) then
				targets_list:append(target)
			end
		end
		if targets_list:length() > 0 then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("kesxzhiyi")
			room:useCard(sgs.CardUseStruct(slash, source, targets_list))
		end
	end
}
kesxzhiyiVS = sgs.CreateViewAsSkill{
	name = "kesxzhiyi" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		return false
	end ,
	view_as = function(self, cards)
		return kesxzhiyiCard:clone()
	end ,
	enabled_at_play = function()
		return false
	end ,
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@kesxzhiyi")
	end
}
kesxzhiyi = sgs.CreateTriggerSkill{
	name = "kesxzhiyi" ,
	events = {sgs.EventPhaseChanging,sgs.CardUsed} ,
	view_as_skill = kesxzhiyiVS ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				room:setPlayerMark(player,"&kesxzhiyi-Clear",1)
			end
		end
		if (event == sgs.EventPhaseChanging) then
			local change = data:toPhaseChange()
			if (change.to == sgs.Player_NotActive) and (player:getMark("&kesxzhiyi-Clear") > 0) then
				room:sendCompulsoryTriggerLog(player, self:objectName())
				if not sgs.Slash_IsAvailable(player) then
					room:broadcastSkillInvoke(self:objectName())
					player:drawCards(1,self:objectName())
				else
					local result = room:askForChoice(player, self:objectName(), "draw+sha")
					if result == "sha" then
						if not room:askForUseCard(player, "@@kesxzhiyi", "kesxzhiyi-ask", 1) then
							room:broadcastSkillInvoke(self:objectName())
							player:drawCards(1,self:objectName())
						end
					else
						room:broadcastSkillInvoke(self:objectName())
						player:drawCards(1,self:objectName())
					end
				end
			end
		end
	end
}
kesxzhangyi:addSkill(kesxzhiyi)

sgs.LoadTranslationTable{

	["kesxzhangyi"] = "张翼[离]", 
	["&kesxzhangyi"] = "张翼",
	["#kesxzhangyi"] = "亢锐怀忠",
	["designer:kesxzhangyi"] = "官方",
	["cv:kesxzhangyi"] = "官方",
	["illustrator:kesxzhangyi"] = "鬼画府",

	["kesxzhiyi"] = "执义",
	["kesxzhiyi:sha"] = "视为使用一张【杀】",
	["kesxzhiyi:draw"] = "摸一张牌",
	["kesxzhiyi-ask"] = "执义：你可以视为使用一张【杀】",
	[":kesxzhiyi"] = "锁定技，每个回合结束时，若你本回合使用过【杀】，你摸一张牌或视为使用一张【杀】。",

	["$kesxzhiyi1"] = "伯约勿扰，吾来助你！",
	["$kesxzhiyi2"] = "众将听令，此战可进不可退！",

	["~kesxzhangyi"] = "主公，季汉亡矣！",
}

kesxjianggan = sgs.General(extension_zhen, "kesxjianggan", "wei", 3, true)

kesxdaoshu = sgs.CreateTriggerSkill{
	name = "kesxdaoshu",
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start then
				local jgs = room:findPlayersBySkillName(self:objectName())
				for _, jg in sgs.qlist(jgs) do
					if (jg:getMark("&usekesxdaoshu_lun") == 0) then
						local canchooses = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getOtherPlayers(player)) do
							if not p:isKongcheng() then
								canchooses:append(p)
							end
						end
						local target = room:askForPlayerChosen(jg, canchooses, self:objectName(), "kesxdaoshu-ask", true, true)
						if target then
							room:broadcastSkillInvoke(self:objectName())
							room:setPlayerMark(jg,"&usekesxdaoshu_lun",1)
							local card_id = room:askForCardChosen(jg, target, "h", self:objectName())
							local thecard = sgs.Sanguosha:getCard(card_id)
							room:setPlayerMark(player, "&kesxdaoshu+:+"..thecard:getSuitString().."+-Clear",1)
							room:setPlayerMark(jg, "&kesxdaoshu+:+"..thecard:getSuitString().."+-Clear",1)
							local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, jg:objectName())
							room:showCard(target,card_id)
							room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
						end
					end
				end
			end
		end
	end,
	can_trigger = function(self,target)
		return target 
	end,
}
kesxjianggan:addSkill(kesxdaoshu)

kesxdaoshuex = sgs.CreateCardLimitSkill{
	name = "#kesxdaoshuex",
	limit_list = function(self, player, card)
		--if (player:getMark("&kesxdaoshu+:+"..card:getSuitString().."+-Clear") > 0) then
			return "use"
		--else
			--return ""
		--end
	end,
	limit_pattern = function(self, player, card)
		if (player:getMark("&kesxdaoshu+:+"..card:getSuitString().."+-Clear") > 0) then
			return ".|.|.|hand"
		else
			return ""
		end
	end
}
kesxjianggan:addSkill(kesxdaoshuex)
--extension_zhen:insertRelatedSkills("kesxdaoshu", "#kesxdaoshuex")

kesxdaizui = sgs.CreateTriggerSkill{
	name = "kesxdaizui",
	events = {sgs.Damaged},
	frequency = sgs.Skill_Frequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Damaged) then
			if player:getMark("&usekesxdaoshu_lun") > 0 then
				room:sendCompulsoryTriggerLog(player,self:objectName())
				room:broadcastSkillInvoke(self:objectName())
				room:setPlayerMark(player,"&usekesxdaoshu_lun", 0)
			end
		end
	end
}
kesxjianggan:addSkill(kesxdaizui)

sgs.LoadTranslationTable{

	["kesxjianggan"] = "蒋干[震]", 
	["&kesxjianggan"] = "蒋干",
	["#kesxjianggan"] = "独步江淮",
	["designer:kesxjianggan"] = "官方",
	["cv:kesxjianggan"] = "官方",
	["illustrator:kesxjianggan"] = "官方",

	["kesxdaoshu"] = "盗书",
	["usekesxdaoshu"] = "已盗书",
	["kesxdaoshu-ask"] = "你可以选择发动“盗书”的角色",
	[":kesxdaoshu"] = "每轮限一次，一名角色的准备阶段，你可以展示另一名角色的一张手牌并令其获得之，然后你与其本回合不能使用与该牌花色相同的手牌。",

	["kesxdaizui"] = "戴罪",
	[":kesxdaizui"] = "当你受到伤害后，你本轮视为未发动过“盗书”。",

	["$kesxdaoshu1"] = "在此机要之地，何不一窥东吴军机。",
	["$kesxdaoshu2"] = "哦？密信……果然有所收获。",
	["$kesxdaizui1"] = "望丞相权且记过，容干将功折罪啊！",
	["$kesxdaizui2"] = "干，谢丞相不杀之恩！",

	["~kesxjianggan"] = "唉！假信害我不浅啊……",
}

kesxmayunlu = sgs.General(extension_zhen, "kesxmayunlu", "shu", 4, false)

kesxfenghun = sgs.CreateTriggerSkill{
	name = "kesxfenghun",
	events = {sgs.ConfirmDamage,sgs.TargetSpecified,sgs.CardFinished},
	frequency = sgs.Skill_NotFrequent, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetSpecified) or (event == sgs.CardFinished) then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				if event == sgs.TargetSpecified then
					for _, p in sgs.qlist(use.to) do
						room:setPlayerMark(p,"kesxfenghuntarget-Clear",1)
					end
				else
					for _, p in sgs.qlist(room:getAllPlayers()) do
						room:setPlayerMark(p,"kesxfenghuntarget-Clear",0)
					end
				end
			end
		end
		if (event == sgs.ConfirmDamage) then
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("Slash") 
			and (damage.to:getMark("kesxfenghuntarget-Clear") > 0) then
				local choices = {}
				if player:canDiscard(player, "he") then 
					table.insert(choices, "selfdis")
				end
				if player:canDiscard(damage.to, "he") then
					table.insert(choices, "hedis")
				end
				if #choices > 0 then
					table.insert(choices, "cancel")
				end
				if #choices > 1 then
					local choice = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
					room:broadcastSkillInvoke(self:objectName())
					if choice == "selfdis" then
						local to_throw = room:askForCardChosen(player, player, "he", self:objectName())
						local card = sgs.Sanguosha:getCard(to_throw)
						room:throwCard(card, player, player)
						if (card:getSuit() == sgs.Card_Diamond) then
							room:sendCompulsoryTriggerLog(player,self:objectName())
							local hurt = damage.damage
							damage.damage = 1 + hurt
							data:setValue(damage)
						end
					elseif choice == "hedis" then
						local to_throw = room:askForCardChosen(player, damage.to, "he", self:objectName())
						local card = sgs.Sanguosha:getCard(to_throw)
						room:throwCard(card, damage.to, player)
						if (card:getSuit() == sgs.Card_Diamond) then
							room:sendCompulsoryTriggerLog(player,self:objectName())
							local hurt = damage.damage
							damage.damage = 1 + hurt
							data:setValue(damage)
						end
					end
				end
			end
		end
	end
}
kesxmayunlu:addSkill(kesxfenghun)

kesxmayunlu:addSkill("mashu")

sgs.LoadTranslationTable{

	["kesxmayunlu"] = "马云騄[震]", 
	["&kesxmayunlu"] = "马云騄",
	["#kesxmayunlu"] = "剑胆琴心",
	["designer:kesxmayunlu"] = "官方",
	["cv:kesxmayunlu"] = "官方",
	["illustrator:kesxmayunlu"] = "叶碧芳",

	["kesxfenghun"] = "凤魂",
	["kesxfenghun:selfdis"] = "弃置自己的一张牌",
	["kesxfenghun:hedis"] = "弃置其一张牌",
	["kesxfenghun:cancel"] = "取消",
	[":kesxfenghun"] = "当你使用【杀】对目标角色造成伤害时，你可以弃置你或其一张牌，若此牌为♦，此伤害+1。",


	["$kesxfenghun1"] = "贼人是不是被本姑娘给吓破胆了呀？",
	["$kesxfenghun2"] = "看我不好好杀杀你的威风！",

	["~kesxmayunlu"] = "子龙哥哥，救我~",
}

kesxmateng = sgs.General(extension_zhen, "kesxmateng$", "qun", 4, true)

kesxxiongyiCard = sgs.CreateSkillCard{
	name = "kesxxiongyiCard",
	will_throw = false,
	filter = function(self, selected, to_select)
		return (#selected < 99) 
	end,
	on_use = function(self, room, source, targets)
		local yes = 1
		while yes == 1 do
			for _, p in ipairs(targets) do
				local cantargets = sgs.SPlayerList()
				for _, pp in sgs.qlist(room:getAllPlayers()) do
					if p:canSlash(pp,false) then cantargets:append(pp) end
				end
				if not room:askForUseSlashTo(p, cantargets,"kesxxiongyi-ask",true,false,false,nil,nil,"kesxxiongyiflag") then
					yes = 0
					break
				end
			end
		end
	end,
}
kesxxiongyiVS = sgs.CreateZeroCardViewAsSkill{
	name = "kesxxiongyi",
	view_as = function(self, cards)
		return kesxxiongyiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@kesxxiongyi") >= 1
	end
}

kesxxiongyi = sgs.CreateTriggerSkill{
	name = "kesxxiongyi",
	view_as_skill = kesxxiongyiVS,
	events = {sgs.TargetConfirmed,sgs.TargetSpecified} ,
	frequency = sgs.Skill_Limited,
	limit_mark = "@kesxxiongyi",
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.TargetSpecified) then	
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") and use.card:hasFlag("kesxxiongyiflag") then
				local log = sgs.LogMessage()
				log.type = "$kesxxiongyilog"
				log.from = player
				room:sendLog(log)
				local no_respond_list = use.no_respond_list
				for _, szm in sgs.qlist(use.to) do
					table.insert(no_respond_list, szm:objectName())
				end
				use.no_respond_list = no_respond_list
				data:setValue(use)	
			end
		end
	end
}
kesxmateng:addSkill(kesxxiongyi)

kesxmateng:addSkill("mashu")

kesxyouqi = sgs.CreateTriggerSkill{
	name = "kesxyouqi$",
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if (event == sgs.EventPhaseStart) and player:hasLordSkill(self:objectName())
		and (player:getPhase() == sgs.Player_Start) then
			local choosetargets = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if --[[(p:getKingdom() == "qun") and]] ((p:getOffensiveHorse() ~= nil) or (p:getDefensiveHorse() ~= nil)) then
				    --检查能否移到其他角色的区域内
					for _, pto in sgs.qlist(room:getOtherPlayers(p)) do
						if ((p:getOffensiveHorse() ~= nil) and pto:hasOffensiveHorseArea() 
						and (not pto:getOffensiveHorse() ~= nil))
						or
						((p:getDefensiveHorse() ~= nil) and pto:hasDefensiveHorseArea() 
						and (not (pto:getDefensiveHorse() ~= nil))) then
							choosetargets:append(p)
							break
						end
					end
				end
			end
			if choosetargets:length() <= 0 then return false end
			local eny = room:askForPlayerChosen(player, choosetargets, self:objectName(), "kesxyouqi-ask",true,true)
			if eny then
				room:broadcastSkillInvoke(self:objectName())
				local choices = {}
				if eny:getOffensiveHorse() ~= nil then 
					table.insert(choices, "offhorse")
				end
				if eny:getDefensiveHorse() ~= nil then
					table.insert(choices, "defhorse")
				end
				local choice = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				if choice == "offhorse" then
					local totargets = sgs.SPlayerList()
					for _, pto in sgs.qlist(room:getOtherPlayers(eny)) do
						if pto:hasOffensiveHorseArea() and (not pto:getOffensiveHorse() ~= nil) then
							totargets:append(pto)
						end
					end
					local fri = room:askForPlayerChosen(player, totargets, self:objectName(), "kesxyouqito-ask",true,false)
					if fri then
						local off = eny:getOffensiveHorse()
						room:moveCardTo(off, fri, sgs.Player_PlaceEquip)
					end
				end
			end
		end
	end
}
kesxmateng:addSkill(kesxyouqi)

sgs.LoadTranslationTable{

	["kesxmateng"] = "马腾[震]", 
	["&kesxmateng"] = "马腾",
	["#kesxmateng"] = "勇冠西州",
	["designer:kesxmateng"] = "官方",
	["cv:kesxmateng"] = "官方",
	["illustrator:kesxmateng"] = "峰雨同程",

	["kesxxiongyi"] = "雄异",
	[":kesxxiongyi"] = "限定技，出牌阶段，你可以令任意名角色依次选择是否使用一张【杀】，然后这些角色重复此流程，直到其中一名角色选择否。",


	["$"] = "",
	["$"] = "",

	["~kesxmateng"] = "",
}












sgs.Sanguosha:addSkills(skills)
return {extension_li, extension_zhen}

