--==《》==--
extension = sgs.Package("liumuyebao", sgs.Package_GeneralPack)
local skills = sgs.SkillList()

kelinggongji = sgs.General(extension, "kelinggongji", "god", 4)

kechuifuCard = sgs.CreateSkillCard{
	name = "kechuifuCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		return (#targets < 1) and (to_select:getMark("usedchuifu-Clear") == 0)
	end,
	on_use = function(self, room, player, targets)
		local target = targets[1]
		room:setPlayerMark(player,"&banchuifu-Clear",1)
		room:setPlayerMark(target,"usedchuifu-Clear",1)
		local disnum = target:getHandcardNum()
		target:throwAllHandCards()
		local mo = math.min(target:getMaxHp() - target:getHandcardNum(),5)
		if (disnum < target:getHp()) and (mo < 5) then 
			mo = mo + 1
		elseif (disnum > target:getHp()) then
			room:setPlayerMark(player,"&banchuifu-Clear",0)
		end
		target:drawCards(mo,"kechuifu")
	end
}
--主技能
kechuifuVS = sgs.CreateViewAsSkill{
	name = "kechuifu",
	n = 0,
	view_as = function(self, cards)
		return kechuifuCard:clone()
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("&banchuifu-Clear") == 0)
	end, 
}

kechuifu = sgs.CreateTriggerSkill{
	name = "kechuifu",
	view_as_skill = kechuifuVS,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Damaged) and (player:getMark("&banchuifu-Clear") == 0) then
			local damage = data:toDamage()
			local chooses = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if (p:getMark("usedchuifu-Clear") == 0) then
					chooses:append(p)
				end
			end
			if chooses:length() > 0 then
				local eny = room:askForPlayerChosen(player, chooses, self:objectName(), "kechuifu-ask",true,true)
				if eny then
					room:broadcastSkillInvoke(self:objectName())
					room:setPlayerMark(player,"&banchuifu-Clear",1)
					room:setPlayerMark(eny,"usedchuifu-Clear",1)
					local disnum = eny:getHandcardNum()
					eny:throwAllHandCards("kechuifu")
					local mo = math.min(eny:getMaxHp() - eny:getHandcardNum(),5)
					if (disnum < eny:getHp()) and (mo < 5) then 
						mo = mo + 1
					elseif (disnum > eny:getHp()) then
						room:setPlayerMark(player,"&banchuifu-Clear",0)
					end
					eny:drawCards(mo,"kechuifu")
				end
			end
		end
	end,
}
kelinggongji:addSkill(kechuifu)

kelongjuan = sgs.CreateTriggerSkill{
	name = "kelongjuan",
	events = {sgs.CardsMoveOneTime},
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.from 
			and (move.from:objectName() == player:objectName()) 
			and (move.card_ids:length() >= 2)
			and player:hasSkill(self:objectName())
			and (move.reason.m_reason ~= sgs.CardMoveReason_S_REASON_USE) 
			and (move.reason.m_reason ~= sgs.CardMoveReason_S_REASON_RESPONSE) then
				if room:askForSkillInvoke(player, self:objectName(), data) then
					room:broadcastSkillInvoke(self:objectName())
					local result = room:askForChoice(player,self:objectName(),"da+qi")
					if result == "da" then
						local eny = room:askForPlayerChosen(player, room:getAllPlayers(), self:objectName(), "kelongjuanda-ask",false,true)
						if eny then
							room:damage(sgs.DamageStruct(self:objectName(), player, eny))
						end
					else
						local chooses = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getAllPlayers()) do
							if player:canDiscard(p, "he") then
								chooses:append(p)
							end
						end
						if chooses:length() > 0 then
							local eny = room:askForPlayerChosen(player, chooses, self:objectName(), "kelongjuanqi-ask",true,true)
							if eny then
								local to_throw = room:askForCardChosen(player, eny, "he", self:objectName())
								local card = sgs.Sanguosha:getCard(to_throw)
								room:throwCard(card, eny, player)
								local choosest = sgs.SPlayerList()
								for _, p in sgs.qlist(room:getAllPlayers()) do
									if player:canDiscard(p, "he") then
										choosest:append(p)
									end
								end
								if choosest:length() > 0 then
									local eny = room:askForPlayerChosen(player, chooses, self:objectName(), "kelongjuanqi-ask",true,true)
									local to_throw = room:askForCardChosen(player, eny, "he", self:objectName())
									local card = sgs.Sanguosha:getCard(to_throw)
									room:throwCard(card, eny, player)
								end
							end
						end
					end
				end				
			end
		end
	end,
	can_trigger = function(self, player)
		return player
	end,
}
kelinggongji:addSkill(kelongjuan)

sgs.LoadTranslationTable{

	["kelinggongji"] = "凌公绩", 
	["&kelinggongji"] = "凌公绩",
	["#kelinggongji"] = "风神",
	["designer:kelinggongji"] = "流木野",
	["cv:kelinggongji"] = "官方",
	["illustrator:kelinggongji"] = "官方",

	["kechuifu"] = "吹拂",
	["banchuifu"] = "已使用吹拂",
	["kechuifu-ask"] = "请选择发动“吹拂”的角色",
	["keshuaixiangruchoose"] = "",
	[":kechuifu"] = "每回合限一次，出牌阶段或当你受到伤害后，你可令一名本回合未指定过的角色弃置所有手牌，然后将手牌摸至体力上限（至多摸五张），若以此法弃置的牌：小于其体力值，其摸牌数+1；大于其体力值，重置“吹拂”。",

	["kelongjuan"] = "龙卷",
	["kelongjuanda-ask"] = "龙卷：你可以对一名角色造成1点伤害",
	["kelongjuanqi-ask"] = "龙卷：你可以弃置一名角色一张牌",
	["kelongjuan:da"] = "对一名角色造成1点伤害",
	["kelongjuan:qi"] = "弃置任意名角色至多两张牌",
	[":kelongjuan"] = "当你不因使用或打出而一次性失去至少两张牌后，你可选择一项：1，对一名角色造成1点伤害；2，弃置任意名角色至多两张牌。",

	["$kechuifu1"] = "攻略沙场，焉较一衣一甲之得失？",
	["$kechuifu2"] = "睚敌眦仇，岂留一兵一卒之后患？",
	["$kelongjuan1"] = "急军先行，斩将，夺城，再败军！",
	["$kelongjuan2"] = "短兵相接，教尔等片甲不留！",

	["~kelinggongji"] = "公绩之犬子，就托于主公了……",
}
























sgs.LoadTranslationTable{
    ["liumuyebao"] = "流木野包",

}

sgs.Sanguosha:addSkills(skills)
return {extension}

